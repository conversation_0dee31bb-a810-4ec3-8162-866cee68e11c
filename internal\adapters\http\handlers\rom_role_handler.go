package handlers

import (
	"strconv"

	"github.com/SneatX/phillips_go/internal/adapters/http/response"
	"github.com/SneatX/phillips_go/internal/core/port/in"
	"github.com/SneatX/phillips_go/internal/dto"
	appErrors "github.com/SneatX/phillips_go/internal/shared/errors"
	"github.com/gin-gonic/gin"
)

type RomRoleHandler struct {
	romRoleService in.RomRoleService
}

func NewRomRoleHandler(service in.RomRoleService) *RomRoleHandler {
	return &RomRoleHandler{romRoleService: service}
}

func (h *RomRoleHandler) GetRolesByRomID(c *gin.Context) {
	romIDStr := c.Param("romId")
	romID, err := strconv.ParseUint(romIDStr, 10, 32)
	if err != nil {
		response.ErrorResponse(c, appErrors.NewValidationError("Invalid rom ID format. Must be a positive integer."))
		return
	}

	romRoles, err := h.romRoleService.GetRolesByRomID(uint(romID))
	if err != nil {
		_ = c.Error(err)
		return
	}

	romRolesResponse := dto.NewRomRolesResponse(uint(romID), romRoles, "Rom roles retrieved successfully")
	response.SuccessMany(c, romRolesResponse)
}

func (h *RomRoleHandler) GetRomsByRoleID(c *gin.Context) {
	roleIDStr := c.Param("roleId")
	roleID, err := strconv.ParseUint(roleIDStr, 10, 32)
	if err != nil {
		response.ErrorResponse(c, appErrors.NewValidationError("Invalid role ID format. Must be a positive integer."))
		return
	}

	romRoles, err := h.romRoleService.GetRomsByRoleID(uint(roleID))
	if err != nil {
		_ = c.Error(err)
		return
	}

	roleRomsResponse := dto.NewRoleRomsResponse(uint(roleID), romRoles, "Role roms retrieved successfully")
	response.SuccessMany(c, roleRomsResponse)
}

func (h *RomRoleHandler) GetByRomAndRole(c *gin.Context) {
	romIDStr := c.Param("romId")
	romID, err := strconv.ParseUint(romIDStr, 10, 32)
	if err != nil {
		response.ErrorResponse(c, appErrors.NewValidationError("Invalid rom ID format. Must be a positive integer."))
		return
	}

	roleIDStr := c.Param("roleId")
	roleID, err := strconv.ParseUint(roleIDStr, 10, 32)
	if err != nil {
		response.ErrorResponse(c, appErrors.NewValidationError("Invalid role ID format. Must be a positive integer."))
		return
	}

	romRole, err := h.romRoleService.GetByRomAndRole(uint(romID), uint(roleID))
	if err != nil {
		_ = c.Error(err)
		return
	}

	response.SuccessOne(c, dto.FromDomainRomRole(*romRole))
}

func (h *RomRoleHandler) AddRoleToRom(c *gin.Context) {
	var req dto.AddRoleToRomRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ErrorResponse(c, appErrors.NewValidationError("Invalid request body", err))
		return
	}

	romRoleToCreate := dto.ToDomainRomRole(&req)
	createdRomRole, err := h.romRoleService.AddRoleToRom(romRoleToCreate)
	if err != nil {
		_ = c.Error(err)
		return
	}

	response.SuccessOne(c, dto.FromDomainRomRole(*createdRomRole))
}

func (h *RomRoleHandler) UpdateRoleQuantity(c *gin.Context) {
	romIDStr := c.Param("romId")
	romID, err := strconv.ParseUint(romIDStr, 10, 32)
	if err != nil {
		response.ErrorResponse(c, appErrors.NewValidationError("Invalid rom ID format. Must be a positive integer."))
		return
	}

	roleIDStr := c.Param("roleId")
	roleID, err := strconv.ParseUint(roleIDStr, 10, 32)
	if err != nil {
		response.ErrorResponse(c, appErrors.NewValidationError("Invalid role ID format. Must be a positive integer."))
		return
	}

	var req dto.UpdateRoleQuantityRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ErrorResponse(c, appErrors.NewValidationError("Invalid request body", err))
		return
	}

	romRoleToUpdate := dto.ToUpdateDomainRomRole(&req, uint(romID), uint(roleID))
	updatedRomRole, err := h.romRoleService.UpdateRoleQuantity(romRoleToUpdate)
	if err != nil {
		_ = c.Error(err)
		return
	}

	response.SuccessOne(c, dto.FromDomainRomRole(*updatedRomRole))
}

func (h *RomRoleHandler) RemoveRoleFromRom(c *gin.Context) {
	romIDStr := c.Param("romId")
	romID, err := strconv.ParseUint(romIDStr, 10, 32)
	if err != nil {
		response.ErrorResponse(c, appErrors.NewValidationError("Invalid rom ID format. Must be a positive integer."))
		return
	}

	roleIDStr := c.Param("roleId")
	roleID, err := strconv.ParseUint(roleIDStr, 10, 32)
	if err != nil {
		response.ErrorResponse(c, appErrors.NewValidationError("Invalid role ID format. Must be a positive integer."))
		return
	}

	err = h.romRoleService.RemoveRoleFromRom(uint(romID), uint(roleID))
	if err != nil {
		_ = c.Error(err)
		return
	}

	response.SuccessOne(c, gin.H{"message": "Role removed from rom successfully"})
}

func (h *RomRoleHandler) RemoveAllRolesFromRom(c *gin.Context) {
	romIDStr := c.Param("romId")
	romID, err := strconv.ParseUint(romIDStr, 10, 32)
	if err != nil {
		response.ErrorResponse(c, appErrors.NewValidationError("Invalid rom ID format. Must be a positive integer."))
		return
	}

	err = h.romRoleService.RemoveAllRolesFromRom(uint(romID))
	if err != nil {
		_ = c.Error(err)
		return
	}

	response.SuccessOne(c, gin.H{"message": "All roles removed from rom successfully"})
}
