package handlers

import (
	"strconv"

	"github.com/SneatX/phillips_go/internal/adapters/http/response"
	"github.com/SneatX/phillips_go/internal/core/port/in"
	"github.com/SneatX/phillips_go/internal/dto"
	appErrors "github.com/SneatX/phillips_go/internal/shared/errors"
	"github.com/gin-gonic/gin"
)

type RomRoleHandler struct {
	romRoleService in.RomRoleService
}

func NewRomRoleHandler(service in.RomRoleService) *RomRoleHandler {
	return &RomRoleHandler{romRoleService: service}
}

func (h *RomRoleHandler) GetRolesByAssignmentID(c *gin.Context) {
	assignmentIDStr := c.Param("assignmentId")
	assignmentID, err := strconv.ParseUint(assignmentIDStr, 10, 32)
	if err != nil {
		response.ErrorResponse(c, appErrors.NewValidationError("Invalid assignment ID format. Must be a positive integer."))
		return
	}

	romRoles, err := h.romRoleService.GetRolesByAssignmentID(uint(assignmentID))
	if err != nil {
		_ = c.Error(err)
		return
	}

	assignmentRolesResponse := dto.NewAssignmentRolesResponse(uint(assignmentID), romRoles, "Assignment roles retrieved successfully")
	response.SuccessMany(c, assignmentRolesResponse)
}

func (h *RomRoleHandler) GetAssignmentsByRoleID(c *gin.Context) {
	roleIDStr := c.Param("roleId")
	roleID, err := strconv.ParseUint(roleIDStr, 10, 32)
	if err != nil {
		response.ErrorResponse(c, appErrors.NewValidationError("Invalid role ID format. Must be a positive integer."))
		return
	}

	romRoles, err := h.romRoleService.GetAssignmentsByRoleID(uint(roleID))
	if err != nil {
		_ = c.Error(err)
		return
	}

	roleAssignmentsResponse := dto.NewRoleAssignmentsResponse(uint(roleID), romRoles, "Role assignments retrieved successfully")
	response.SuccessMany(c, roleAssignmentsResponse)
}

func (h *RomRoleHandler) GetByAssignmentAndRole(c *gin.Context) {
	assignmentIDStr := c.Param("assignmentId")
	assignmentID, err := strconv.ParseUint(assignmentIDStr, 10, 32)
	if err != nil {
		response.ErrorResponse(c, appErrors.NewValidationError("Invalid assignment ID format. Must be a positive integer."))
		return
	}

	roleIDStr := c.Param("roleId")
	roleID, err := strconv.ParseUint(roleIDStr, 10, 32)
	if err != nil {
		response.ErrorResponse(c, appErrors.NewValidationError("Invalid role ID format. Must be a positive integer."))
		return
	}

	romRole, err := h.romRoleService.GetByAssignmentAndRole(uint(assignmentID), uint(roleID))
	if err != nil {
		_ = c.Error(err)
		return
	}

	response.SuccessOne(c, dto.FromDomainRomRole(*romRole))
}

func (h *RomRoleHandler) AddRoleToAssignment(c *gin.Context) {
	var req dto.AddRoleToAssignmentRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ErrorResponse(c, appErrors.NewValidationError("Invalid request body", err))
		return
	}

	romRoleToCreate := dto.ToDomainRomRole(&req)
	createdRomRole, err := h.romRoleService.AddRoleToAssignment(romRoleToCreate)
	if err != nil {
		_ = c.Error(err)
		return
	}

	response.SuccessOne(c, dto.FromDomainRomRole(*createdRomRole))
}

func (h *RomRoleHandler) UpdateRoleQuantity(c *gin.Context) {
	assignmentIDStr := c.Param("assignmentId")
	assignmentID, err := strconv.ParseUint(assignmentIDStr, 10, 32)
	if err != nil {
		response.ErrorResponse(c, appErrors.NewValidationError("Invalid assignment ID format. Must be a positive integer."))
		return
	}

	roleIDStr := c.Param("roleId")
	roleID, err := strconv.ParseUint(roleIDStr, 10, 32)
	if err != nil {
		response.ErrorResponse(c, appErrors.NewValidationError("Invalid role ID format. Must be a positive integer."))
		return
	}

	var req dto.UpdateRoleQuantityRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ErrorResponse(c, appErrors.NewValidationError("Invalid request body", err))
		return
	}

	romRoleToUpdate := dto.ToUpdateDomainRomRole(&req, uint(assignmentID), uint(roleID))
	updatedRomRole, err := h.romRoleService.UpdateRoleQuantity(romRoleToUpdate)
	if err != nil {
		_ = c.Error(err)
		return
	}

	response.SuccessOne(c, dto.FromDomainRomRole(*updatedRomRole))
}

func (h *RomRoleHandler) RemoveRoleFromAssignment(c *gin.Context) {
	assignmentIDStr := c.Param("assignmentId")
	assignmentID, err := strconv.ParseUint(assignmentIDStr, 10, 32)
	if err != nil {
		response.ErrorResponse(c, appErrors.NewValidationError("Invalid assignment ID format. Must be a positive integer."))
		return
	}

	roleIDStr := c.Param("roleId")
	roleID, err := strconv.ParseUint(roleIDStr, 10, 32)
	if err != nil {
		response.ErrorResponse(c, appErrors.NewValidationError("Invalid role ID format. Must be a positive integer."))
		return
	}

	err = h.romRoleService.RemoveRoleFromAssignment(uint(assignmentID), uint(roleID))
	if err != nil {
		_ = c.Error(err)
		return
	}

	response.SuccessOne(c, gin.H{"message": "Role removed from assignment successfully"})
}

func (h *RomRoleHandler) RemoveAllRolesFromAssignment(c *gin.Context) {
	assignmentIDStr := c.Param("assignmentId")
	assignmentID, err := strconv.ParseUint(assignmentIDStr, 10, 32)
	if err != nil {
		response.ErrorResponse(c, appErrors.NewValidationError("Invalid assignment ID format. Must be a positive integer."))
		return
	}

	err = h.romRoleService.RemoveAllRolesFromAssignment(uint(assignmentID))
	if err != nil {
		_ = c.Error(err)
		return
	}

	response.SuccessOne(c, gin.H{"message": "All roles removed from assignment successfully"})
}
