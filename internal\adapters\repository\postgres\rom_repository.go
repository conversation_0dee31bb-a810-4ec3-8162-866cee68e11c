package postgres

import (
	"errors"

	"github.com/SneatX/phillips_go/internal/core/domain"
	appErrors "github.com/SneatX/phillips_go/internal/shared/errors"
	"gorm.io/gorm"
)

type romRepository struct {
	DB *gorm.DB
}

func NewRomRepository(DB *gorm.DB) *romRepository {
	return &romRepository{DB: DB}
}

func (r *romRepository) Create(rom *domain.Rom) (*domain.Rom, error) {
	if result := r.DB.Create(&rom); result.Error != nil {
		return nil, appErrors.NewInternalError("failed to create rom", result.Error)
	}
	return rom, nil
}

func (r *romRepository) Update(rom *domain.Rom) (*domain.Rom, error) {

	var existing domain.Rom
	if err := r.DB.First(&existing, rom.ID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, appErrors.NewNotFoundError("rom not found")
		}
		return nil, appErrors.NewInternalError("failed to find rom for update", err)
	}

	if err := r.DB.Model(&existing).Updates(rom).Error; err != nil {
		return nil, err
	}
	return &existing, nil
}
