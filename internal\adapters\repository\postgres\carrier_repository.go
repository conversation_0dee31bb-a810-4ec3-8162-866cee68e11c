package postgres

import (
	"errors"
	"fmt"

	"github.com/SneatX/phillips_go/internal/core/domain"
	appErrors "github.com/SneatX/phillips_go/internal/shared/errors"
	"github.com/jackc/pgx/v5/pgconn"
	"gorm.io/gorm"
)

type carrierRepository struct {
	DB *gorm.DB
}

func NewCarrierRepository(DB *gorm.DB) *carrierRepository {
	return &carrierRepository{DB: DB}
}

func (r *carrierRepository) GetAll() ([]*domain.Carrier, error) {
	var carriers []*domain.Carrier
	if err := r.DB.Preload("Adjuster").Find(&carriers).Error; err != nil {
		return nil, appErrors.NewInternalError("failed to retrieve all carriers", err)
	}
	return carriers, nil
}

func (r *carrierRepository) Create(carrier *domain.Carrier) (*domain.Carrier, error) {
	if result := r.DB.Create(carrier); result.Error != nil {
		var pgErr *pgconn.PgError
		if errors.As(result.Error, &pgErr) {
			fmt.Printf("PostgreSQL error code: %s, message: %s\n", pgErr.Code, pgErr.Message)
			if pgErr.Code == "23505" {
				return nil, appErrors.NewConflictError("assignment with this claim number already exists")
			}
			if pgErr.Code == "23503" {
				return nil, appErrors.NewNotFoundError("foreign key constraint violation, please check that all related entities exist before creating a carrier", result.Error)
			}
		}
	}
	return carrier, nil
}

func (r *carrierRepository) Update(carrier *domain.Carrier) (*domain.Carrier, error) {
	var existing *domain.Carrier
	if err := r.DB.First(&existing, carrier.ID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, appErrors.NewNotFoundError("Carrier not found")
		}
		return nil, appErrors.NewInternalError("Failed to fetch carrier before update", err)
	}

	result := r.DB.Model(&existing).Updates(carrier)
	if err := result.Error; err != nil {
		var pgErr *pgconn.PgError
		if errors.As(err, &pgErr) {
			fmt.Printf("PostgreSQL error code: %s, message: %s\n", pgErr.Code, pgErr.Message)
			if pgErr.Code == "23505" {
				return nil, appErrors.NewConflictError(fmt.Sprintf("Conflict error: %s", err))
			}
			if pgErr.Code == "23503" {
				return nil, appErrors.NewNotFoundError("Adjuster not found — invalid foreign key", err)
			}
		}
		return nil, appErrors.NewInternalError("Failed to update carrier", err)
	}

	return existing, nil
}
