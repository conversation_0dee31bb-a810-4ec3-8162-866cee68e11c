package postgres

import (
	"errors"
	"fmt"

	"github.com/SneatX/phillips_go/internal/core/domain"
	appErrors "github.com/SneatX/phillips_go/internal/shared/errors"
	"github.com/jackc/pgx/v5/pgconn"
	"gorm.io/gorm"
)

type consultantRepository struct {
	DB *gorm.DB
}

func NewConsultantRepository(DB *gorm.DB) *consultantRepository {
	return &consultantRepository{DB: DB}
}

func (r *consultantRepository) GetAll() ([]*domain.Consultant, error) {
	var consultants []*domain.Consultant
	if err := r.DB.Find(&consultants).Error; err != nil {
		return nil, appErrors.NewInternalError("failed to retrieve all consultants from database", err)
	}
	return consultants, nil
}

func (r *consultantRepository) GetByID(id uint) (*domain.Consultant, error) {
	var consultant domain.Consultant
	if err := r.DB.First(&consultant, id).Error; err != nil {
		if err.Error() == "record not found" {
			return nil, appErrors.NewNotFoundError("consultant not found")
		}
		return nil, appErrors.NewInternalError("failed to retrieve consultant from database", err)
	}
	return &consultant, nil
}

func (r *consultantRepository) Create(consultant *domain.Consultant) (*domain.Consultant, error) {
	result := r.DB.Create(&consultant)

	if result.Error != nil {
		var pgErr *pgconn.PgError
		if errors.As(result.Error, &pgErr) {
			fmt.Printf("PostgreSQL error code: %s, message: %s\n", pgErr.Code, pgErr.Message)
			if pgErr.Code == "23505" {
				return nil, appErrors.NewConflictError(fmt.Sprintf("New conflict error, %s", result.Error))
			}
			if pgErr.Code == "23503" {
				return nil, appErrors.NewNotFoundError("foreign key constraint violation, please check that all related entities exist before creating a consultant")
			}
		}
		return nil, appErrors.NewInternalError("failed to create consultant", result.Error)
	}

	return consultant, nil
}

func (r *consultantRepository) Update(consultant *domain.Consultant) (*domain.Consultant, error) {
	result := r.DB.Save(&consultant)

	if result.Error != nil {
		var pgErr *pgconn.PgError
		if errors.As(result.Error, &pgErr) {
			fmt.Printf("PostgreSQL error code: %s, message: %s\n", pgErr.Code, pgErr.Message)
			if pgErr.Code == "23505" {
				return nil, appErrors.NewConflictError(fmt.Sprintf("New conflict error, %s", result.Error))
			}
			if pgErr.Code == "23503" {
				return nil, appErrors.NewNotFoundError("foreign key constraint violation, please check that all related entities exist before updating a consultant")
			}
		}
		return nil, appErrors.NewInternalError("failed to update consultant", result.Error)
	}

	return consultant, nil
}
