package service

import (
	"archive/zip"
	"fmt"
	"io"
	"os"
	"path"
	"path/filepath"
	"strings"
	"time"

	"github.com/SneatX/phillips_go/internal/core/domain"
	"github.com/SneatX/phillips_go/internal/core/port/in"
	"github.com/SneatX/phillips_go/internal/core/port/out"
	jwtAdapter "github.com/SneatX/phillips_go/internal/platform/token"
	"github.com/golang-jwt/jwt/v5"

	appErrors "github.com/SneatX/phillips_go/internal/shared/errors"
)

type documentService struct {
	documentRepo out.DocumentRepository
	blobStorage  out.BlobStorage
	jwtManager   *jwtAdapter.JWTManager
	baseDir      string
}

func NewDocumentService(repo out.DocumentRepository, blobStorage out.BlobStorage, jwtManager *jwtAdapter.JWTManager, baseDir string) in.DocumentService {
	return &documentService{
		documentRepo: repo,
		blobStorage:  blobStorage,
		jwtManager:   jwtManager,
		baseDir:      baseDir,
	}
}

func (s *documentService) GetByAssignmentID(assignmentID uint) ([]*domain.Document, error) {
	return s.documentRepo.GetByAssignmentID(assignmentID)
}

func (s *documentService) GetUploadedByAssignmentID(assignmentID uint) ([]*domain.Document, error) {
	return s.documentRepo.GetUploadedByAssignmentID(assignmentID)
}

func (s *documentService) CreateIntent(documentIntent *domain.Document) (*domain.Document, string, error) {
	document := domain.Document{
		AssignmentID: documentIntent.AssignmentID,
		FileName:     sanitizeFilename(documentIntent.FileName),
		MimeType:     documentIntent.MimeType,
		Size:         documentIntent.Size,
		Status:       "pending",
		CreatedByID:  documentIntent.CreatedByID,
	}
	createdDocument, err := s.documentRepo.CreateIntent(&document)
	if err != nil {
		return nil, "", err
	}

	uploadClaims := jwtAdapter.UploadClaims{
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(5 * time.Minute)),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			Issuer:    "Phillips Upload",
			Subject:   fmt.Sprintf("%d", createdDocument.ID),
		},
		DocumentID:   createdDocument.ID,
		AssignmentID: createdDocument.AssignmentID,
		FileName:     createdDocument.FileName,
	}

	uploadToken, err := s.jwtManager.GenerateTokenWithClaims(uploadClaims)
	if err != nil {
		return nil, "", err
	}

	return createdDocument, uploadToken, nil
}

func (s *documentService) UploadDocument(token, originalName string, r io.Reader) (*domain.Document, error) {
	parsedClaims, err := s.jwtManager.VerifyTokenWithClaims(token, &jwtAdapter.UploadClaims{})
	if err != nil {
		return nil, err
	}

	claims, ok := parsedClaims.(*jwtAdapter.UploadClaims)
	if !ok {
		return nil, appErrors.NewUnauthorizedError("invalid upload token claims")
	}

	doc, err := s.documentRepo.GetByID(claims.DocumentID)
	if err != nil {
		return nil, err
	}

	if doc.Status != "pending" {
		return nil, appErrors.NewConflictError("document not pending", nil)
	}

	rel := filepath.Join(fmt.Sprint(doc.AssignmentID), fmt.Sprint(doc.ID), claims.FileName)

	size, sha, err := s.blobStorage.Save(rel, r)
	if err != nil {
		return nil, err
	}

	doc.Size = size
	doc.Sha256 = sha
	doc.Status = "uploaded"
	doc.FilePath = rel
	doc.FileName = originalName
	createdDoc, err := s.documentRepo.UpdateDocument(doc)
	if err != nil {
		return nil, err
	}

	return createdDoc, nil
}

func (s *documentService) PrepareAssignmentZip(assignmentID uint, docs []*domain.Document) (zipRelPath, zipFileName string, err error) {
	ts := time.Now().UTC().Format("20060102-150405")
	zipFileName = fmt.Sprintf("assignment-%d-%s.zip", assignmentID, ts)
	relDir := path.Join("bundles", fmt.Sprint(assignmentID))
	zipRelPath = path.Join(relDir, zipFileName)

	absBase := s.baseDir
	absZip := filepath.Join(absBase, zipRelPath)

	if err := os.MkdirAll(filepath.Dir(absZip), 0o750); err != nil {
		return "", "", err
	}

	tmp := absZip + ".part"
	f, err := os.Create(tmp)
	if err != nil {
		return "", "", err
	}
	zw := zip.NewWriter(f)

	for _, d := range docs {
		absDoc := filepath.Join(absBase, d.FilePath)

		inZipName := d.FileName
		if inZipName == "" {
			inZipName = filepath.Base(d.FilePath)
		}

		w, err := zw.Create(inZipName)
		if err != nil {
			_ = zw.Close()
			_ = f.Close()
			_ = os.Remove(tmp)
			return "", "", err
		}

		src, err := os.Open(absDoc)
		if err != nil {
			_ = zw.Close()
			_ = f.Close()
			_ = os.Remove(tmp)
			return "", "", err
		}

		if _, err := io.Copy(w, src); err != nil {
			_ = src.Close()
			_ = zw.Close()
			_ = f.Close()
			_ = os.Remove(tmp)
			return "", "", err
		}
		_ = src.Close()
	}

	if err := zw.Close(); err != nil {
		_ = f.Close()
		_ = os.Remove(tmp)
		return "", "", err
	}
	if err := f.Close(); err != nil {
		_ = os.Remove(tmp)
		return "", "", err
	}

	if err := os.Rename(tmp, absZip); err != nil {
		_ = os.Remove(tmp)
		return "", "", err
	}
	return zipRelPath, zipFileName, nil
}

func sanitizeFilename(name string) string {
	base := filepath.Base(name)
	base = strings.ReplaceAll(base, " ", "_")
	base = strings.Map(func(r rune) rune {
		switch {
		case r >= 'a' && r <= 'z', r >= 'A' && r <= 'Z', r >= '0' && r <= '9',
			r == '.', r == '-', r == '_':
			return r
		default:
			return -1
		}
	}, base)
	return base
}
