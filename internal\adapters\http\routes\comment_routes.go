package routes

import (
	"github.com/SneatX/phillips_go/internal/adapters/http/handlers"
	"github.com/SneatX/phillips_go/internal/adapters/http/middleware"
	jwt "github.com/SneatX/phillips_go/internal/platform/token"
	"github.com/gin-gonic/gin"
)

func RegisterCommentRoutes(
	rg *gin.RouterGroup,
	commentHandler *handlers.CommentHandler,
	jwtManager *jwt.JWTManager,
) {
	protectedComments := rg.Group("/comments", middleware.JWTAuthMiddleware(jwtManager))
	{
		protectedComments.GET("/:id", commentHandler.GetCommentByID)
		protectedComments.GET("/assignment/:assignmentId", commentHandler.GetCommentsByAssignmentID)
		protectedComments.POST("", commentHandler.CreateComment)
		protectedComments.PUT("/:id", commentHandler.UpdateComment)
		protectedComments.DELETE("/:id", commentHandler.DeleteComment)
	}
}
