package postgres

import (
	"github.com/SneatX/phillips_go/internal/core/domain"
	appErrors "github.com/SneatX/phillips_go/internal/shared/errors"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type categoryRepository struct {
	DB *gorm.DB
}

func NewCategoryRepository(DB *gorm.DB) *categoryRepository {
	return &categoryRepository{DB: DB}
}

func (r *categoryRepository) GetAll() ([]*domain.Category, error) {
	var categories []*domain.Category
	if err := r.DB.Find(&categories).Error; err != nil {
		return nil, appErrors.NewInternalError("failed to retrieve all categories", err)
	}
	return categories, nil
}

func (r *categoryRepository) Create(category *domain.Category) (*domain.Category, error) {
	if result := r.DB.Create(&category); result.Error != nil {
		return nil, appErrors.NewInternalError("failed to create category", result.Error)
	}
	return category, nil
}

func (r *categoryRepository) Update(category *domain.Category) (*domain.Category, error) {
	if result := r.DB.Model(&category).Updates(&category); result.Error != nil {
		return nil, appErrors.NewInternalError("failed to update category", result.Error)
	}
	return category, nil
}

func (r *categoryRepository) CreateAssignmentCategory(assignmentCategory *domain.AssignmentCategory) (*domain.AssignmentCategory, error) {
	// if there is already a row with the same assignment_id and category_id, do nothing
	if result := r.DB.Clauses(clause.OnConflict{
		Columns:   []clause.Column{{Name: "assignment_id"}, {Name: "category_id"}},
		DoNothing: true,
	}).Create(&assignmentCategory); result.Error != nil {
		return nil, appErrors.NewInternalError("failed to create assignment category", result.Error)
	}

	return assignmentCategory, nil
}

func (r *categoryRepository) PatchAssignmentCategory(assignmentCategory *domain.AssignmentCategory) (*domain.AssignmentCategory, error) {

	if err := r.DB.
		Model(&domain.AssignmentCategory{}).
		Where("assignment_id = ? AND category_id = ?", assignmentCategory.AssignmentID, assignmentCategory.CategoryID).
		Updates(assignmentCategory).Error; err != nil {
		return nil, appErrors.NewInternalError("failed to update assignment category", err)
	}
	if err := r.DB.
		Preload("Category").
		First(assignmentCategory, "assignment_id = ? AND category_id = ?", assignmentCategory.AssignmentID, assignmentCategory.CategoryID).Error; err != nil {
		return nil, appErrors.NewInternalError("failed to preload category", err)
	}
	return assignmentCategory, nil
}
