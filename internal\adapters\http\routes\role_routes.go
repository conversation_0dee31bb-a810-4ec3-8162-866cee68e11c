package routes

import (
	"github.com/SneatX/phillips_go/internal/adapters/http/handlers"
	"github.com/SneatX/phillips_go/internal/adapters/http/middleware"
	jwt "github.com/SneatX/phillips_go/internal/platform/token"
	"github.com/gin-gonic/gin"
)

func RegisterRoleRoutes(rg *gin.RouterGroup, handler *handlers.RoleHandler, jwtManager *jwt.JWTManager) {
	publicRoles := rg.Group("/roles")

	protectedRoles := publicRoles.Group("", middleware.JWTAuthMiddleware(jwtManager))
	{
		protectedRoles.GET("", handler.GetAllRoles)
		protectedRoles.GET("/:id", handler.GetRoleByID)
		protectedRoles.POST("", handler.CreateRole)
		protectedRoles.PUT("/:id", handler.UpdateRole)
		protectedRoles.DELETE("/:id", handler.DeleteRole)
	}
}
