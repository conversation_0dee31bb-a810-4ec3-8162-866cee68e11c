package service

import (
	"errors"
	"fmt"

	"github.com/SneatX/phillips_go/internal/core/domain"
	"github.com/SneatX/phillips_go/internal/core/port/in"
	"github.com/SneatX/phillips_go/internal/core/port/out"

	appErrors "github.com/SneatX/phillips_go/internal/shared/errors"

	"github.com/SneatX/phillips_go/internal/shared/crypto"
)

type userService struct {
	userRepo out.UserRepository
}

func NewUserService(repo out.UserRepository) in.UserService {
	return &userService{userRepo: repo}
}

func (s *userService) GetByID(id uint) (*domain.User, error) {
	user, err := s.userRepo.GetByID(id)
	if err != nil {
		//Example of how can I manage errors in servise layer
		var notFoundErr appErrors.AppError
		if errors.As(err, &notFoundErr) && notFoundErr.Type() == appErrors.TypeNotFound {
			return nil, notFoundErr
		}
		fmt.Printf("DEBUG: Unexpected error from repository for user ID %d: %v\n", id, err)
		return nil, err
	}

	return user, nil
}

func (s *userService) GetCurrentUser(id uint) (*domain.User, error) {
	user, err := s.userRepo.GetByID(id)
	if err != nil {
		var notFoundErr appErrors.AppError
		if errors.As(err, &notFoundErr) && notFoundErr.Type() == appErrors.TypeNotFound {
			return nil, appErrors.NewNotFoundError("current user not found")
		}
		fmt.Printf("DEBUG: Unexpected error from repository for current user ID %d: %v\n", id, err)
		return nil, err
	}

	return user, nil
}

func (s *userService) GetAll() ([]domain.User, error) {
	return s.userRepo.GetAll()
}

func (s *userService) Create(user domain.User) (*domain.User, error) {
	hashPassword, err := crypto.HashPassword(user.Password)
	if err != nil {
		return nil, err
	}

	user.Password = hashPassword
	createdUser, err := s.userRepo.Create(user)
	if err != nil {
		return nil, err
	}

	return createdUser, nil
}

func (s *userService) Authenticate(email, password string) (*domain.User, error) {
	user, err := s.userRepo.GetByEmail(email)
	if err != nil {
		var notFoundErr appErrors.AppError
		if errors.As(err, &notFoundErr) {
			return nil, appErrors.NewUnauthorizedError("invalid email or password")
		}
		return nil, err
	}

	if !crypto.CheckPasswordHash(password, user.Password) {
		return nil, appErrors.NewUnauthorizedError("invalid email or password")
	}

	return user, nil
}
