package postgres

import (
	"errors"
	"fmt"

	"github.com/SneatX/phillips_go/internal/core/domain"
	appErrors "github.com/SneatX/phillips_go/internal/shared/errors"
	"github.com/jackc/pgx/v5/pgconn"
	"gorm.io/gorm"
)

type romRoleRepository struct {
	DB *gorm.DB
}

func NewRomRoleRepository(DB *gorm.DB) *romRoleRepository {
	return &romRoleRepository{DB: DB}
}

func (r *romRoleRepository) GetByRomID(romID uint) ([]*domain.RomRole, error) {
	var romRoles []*domain.RomRole
	if err := r.DB.Preload("Role").Preload("Rom").Where("rom_id = ?", romID).Find(&romRoles).Error; err != nil {
		return nil, appErrors.NewInternalError("failed to retrieve roles for rom", err)
	}
	return romRoles, nil
}

func (r *romRoleRepository) GetByRoleID(roleID uint) ([]*domain.RomRole, error) {
	var romRoles []*domain.RomRole
	if err := r.DB.Preload("Role").Preload("Rom").Where("role_id = ?", roleID).Find(&romRoles).Error; err != nil {
		return nil, appErrors.NewInternalError("failed to retrieve roms for role", err)
	}
	return romRoles, nil
}

func (r *romRoleRepository) GetByRomAndRole(romID, roleID uint) (*domain.RomRole, error) {
	var romRole domain.RomRole
	if err := r.DB.Preload("Role").Preload("Rom").Where("rom_id = ? AND role_id = ?", romID, roleID).First(&romRole).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, appErrors.NewNotFoundError(fmt.Sprintf("role assignment not found for rom %d and role %d", romID, roleID))
		}
		return nil, appErrors.NewInternalError("failed to retrieve role assignment", err)
	}
	return &romRole, nil
}

func (r *romRoleRepository) Create(romRole *domain.RomRole) (*domain.RomRole, error) {
	if result := r.DB.Create(&romRole); result.Error != nil {
		var pgErr *pgconn.PgError
		if errors.As(result.Error, &pgErr) {
			switch pgErr.Code {
			case "23505": // unique_violation
				if pgErr.ConstraintName == "idx_rom_role" {
					return nil, appErrors.NewConflictError("This role is already assigned to this rom")
				}
				return nil, appErrors.NewConflictError("Role assignment already exists")
			case "23503": // foreign_key_violation
				return nil, appErrors.NewValidationError("Invalid rom ID or role ID")
			}
		}
		return nil, appErrors.NewInternalError("failed to create role assignment", result.Error)
	}
	return romRole, nil
}

func (r *romRoleRepository) Update(romRole *domain.RomRole) (*domain.RomRole, error) {
	if result := r.DB.Model(&romRole).Where("rom_id = ? AND role_id = ?", romRole.RomID, romRole.RoleID).Updates(&romRole); result.Error != nil {
		return nil, appErrors.NewInternalError("failed to update role assignment", result.Error)
	}
	return romRole, nil
}

func (r *romRoleRepository) Delete(romID, roleID uint) error {
	result := r.DB.Where("rom_id = ? AND role_id = ?", romID, roleID).Delete(&domain.RomRole{})
	if result.Error != nil {
		return appErrors.NewInternalError("failed to delete role assignment", result.Error)
	}
	if result.RowsAffected == 0 {
		return appErrors.NewNotFoundError(fmt.Sprintf("role assignment not found for rom %d and role %d", romID, roleID))
	}
	return nil
}

func (r *romRoleRepository) DeleteByRomID(romID uint) error {
	result := r.DB.Where("rom_id = ?", romID).Delete(&domain.RomRole{})
	if result.Error != nil {
		return appErrors.NewInternalError("failed to delete role assignments for rom", result.Error)
	}
	return nil
}
