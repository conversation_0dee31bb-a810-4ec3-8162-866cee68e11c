package routes

import (
	"github.com/SneatX/phillips_go/internal/adapters/http/handlers"
	"github.com/SneatX/phillips_go/internal/adapters/http/middleware"
	jwt "github.com/SneatX/phillips_go/internal/platform/token"
	"github.com/gin-gonic/gin"
)

func RegisterRomRoleRoutes(rg *gin.RouterGroup, handler *handlers.RomRoleHandler, jwtManager *jwt.JWTManager) {
	publicRomRoles := rg.Group("/rom-roles")

	protectedRomRoles := publicRomRoles.Group("", middleware.JWTAuthMiddleware(jwtManager))
	{
		protectedRomRoles.GET("/roms/:romId", handler.GetRolesByRomID)
		protectedRomRoles.GET("/roles/:roleId", handler.GetRomsByRoleID)
		protectedRomRoles.GET("/roms/:romId/roles/:roleId", handler.GetByRomAndRole)
		protectedRomRoles.POST("", handler.AddRoleToRom)
		protectedRomRoles.PUT("/roms/:romId/roles/:roleId", handler.UpdateRoleQuantity)
		protectedRomRoles.DELETE("/roms/:romId/roles/:roleId", handler.RemoveRoleFromRom)
		protectedRomRoles.DELETE("/roms/:romId", handler.RemoveAllRolesFromRom)
	}
}
