package routes

import (
	"github.com/SneatX/phillips_go/internal/adapters/http/handlers"
	"github.com/SneatX/phillips_go/internal/adapters/http/middleware"
	jwt "github.com/SneatX/phillips_go/internal/platform/token"
	"github.com/gin-gonic/gin"
)

func RegisterRomRoleRoutes(rg *gin.RouterGroup, handler *handlers.RomRoleHandler, jwtManager *jwt.JWTManager) {
	publicRomRoles := rg.Group("/rom-roles")

	protectedRomRoles := publicRomRoles.Group("", middleware.JWTAuthMiddleware(jwtManager))
	{
		// Get roles for a specific assignment
		protectedRomRoles.GET("/assignments/:assignmentId", handler.GetRolesByAssignmentID)
		
		// Get assignments for a specific role
		protectedRomRoles.GET("/roles/:roleId", handler.GetAssignmentsByRoleID)
		
		// Get specific assignment-role relationship
		protectedRomRoles.GET("/assignments/:assignmentId/roles/:roleId", handler.GetByAssignmentAndRole)
		
		// Add role to assignment
		protectedRomRoles.POST("", handler.AddRoleToAssignment)
		
		// Update role quantity for assignment
		protectedRomRoles.PUT("/assignments/:assignmentId/roles/:roleId", handler.UpdateRoleQuantity)
		
		// Remove specific role from assignment
		protectedRomRoles.DELETE("/assignments/:assignmentId/roles/:roleId", handler.RemoveRoleFromAssignment)
		
		// Remove all roles from assignment
		protectedRomRoles.DELETE("/assignments/:assignmentId", handler.RemoveAllRolesFromAssignment)
	}
}
