package response

import (
	appErrors "github.com/SneatX/phillips_go/internal/shared/errors"
	"github.com/gin-gonic/gin"
)

func SuccessOne(c *gin.Context, data any) {
	c.<PERSON>(200, gin.H{"data": data, "error": nil})
}

func SuccessMany(c *gin.Context, data any) {
	c.<PERSON>(200, gin.H{"data": data, "error": nil})
}

func ErrorResponse(c *gin.Context, appErr appErrors.AppError) {
	c.<PERSON>(appErr.HTTPStatus(), gin.H{
		"data": nil,
		"error": gin.H{
			"message": appErr.Error(),
			"code":    appErr.Code(),
			"type":    appErr.Type(),
			"status":  appErr.HTTPStatus(),
		},
	})
}
