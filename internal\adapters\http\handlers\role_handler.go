package handlers

import (
	"strconv"

	"github.com/SneatX/phillips_go/internal/adapters/http/response"
	"github.com/SneatX/phillips_go/internal/core/port/in"
	"github.com/SneatX/phillips_go/internal/dto"
	appErrors "github.com/SneatX/phillips_go/internal/shared/errors"
	"github.com/gin-gonic/gin"
)

type RoleHandler struct {
	roleService in.RoleService
}

func NewRoleHandler(service in.RoleService) *RoleHandler {
	return &RoleHandler{roleService: service}
}

func (h *RoleHandler) GetAllRoles(c *gin.Context) {
	roles, err := h.roleService.GetAll()
	if err != nil {
		_ = c.Error(err)
		return
	}

	listResponse := dto.NewRoleListResponse(roles, "Roles retrieved successfully")
	response.SuccessMany(c, listResponse)
}

func (h *RoleHandler) GetRoleByID(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.ErrorResponse(c, appErrors.NewValidationError("Invalid role ID format. Must be a positive integer."))
		return
	}

	role, err := h.roleService.GetByID(uint(id))
	if err != nil {
		_ = c.Error(err)
		return
	}

	response.SuccessOne(c, dto.FromDomainRole(*role))
}

func (h *RoleHandler) CreateRole(c *gin.Context) {
	var req dto.CreateRoleRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ErrorResponse(c, appErrors.NewValidationError("Invalid request body", err))
		return
	}

	roleToCreate := dto.ToDomainRole(&req)
	createdRole, err := h.roleService.Create(roleToCreate)
	if err != nil {
		_ = c.Error(err)
		return
	}
	response.SuccessOne(c, dto.FromDomainRole(*createdRole))
}

func (h *RoleHandler) UpdateRole(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.ErrorResponse(c, appErrors.NewValidationError("Invalid role ID format. Must be a positive integer."))
		return
	}

	var req dto.UpdateRoleRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ErrorResponse(c, appErrors.NewValidationError("Invalid request body", err))
		return
	}

	roleToUpdate := dto.ToUpdateDomainRole(&req, uint(id))
	updatedRole, err := h.roleService.Update(roleToUpdate)
	if err != nil {
		_ = c.Error(err)
		return
	}

	response.SuccessOne(c, dto.FromDomainRole(*updatedRole))
}

func (h *RoleHandler) DeleteRole(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.ErrorResponse(c, appErrors.NewValidationError("Invalid role ID format. Must be a positive integer."))
		return
	}

	err = h.roleService.Delete(uint(id))
	if err != nil {
		_ = c.Error(err)
		return
	}

	response.SuccessOne(c, gin.H{"message": "Role deleted successfully"})
}
