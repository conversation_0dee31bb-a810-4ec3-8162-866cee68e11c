package config

import (
	"fmt"
	"log"
	"os"

	"github.com/joho/godotenv"
)

type DBConfig struct {
	Host     string
	Port     string
	User     string
	Password string
	Name     string
}

type ServerConfig struct {
	Port    string
	Hosting string
}

type ClientConfig struct {
	Origin string
	Domain string
}

type JWTConfig struct {
	Secret         string
	ExpirationTime string
	CookieName     string
}

type BlobStorageConfig struct {
	BaseDir string
}

type EnvConfig struct {
	Db          DBConfig
	Server      ServerConfig
	Client      ClientConfig
	JWT         JWTConfig
	BlobStorage BlobStorageConfig
}

func getEnv(key, fallBack string) string {
	value, exist := os.LookupEnv(key)
	if exist {
		return value
	}
	fmt.Printf("Environment variable %q not found. Using default: %s\n", key, fallBack)
	return fallBack
}

func LoadEnv() *EnvConfig {

	err := godotenv.Load()
	if err != nil {
		err = godotenv.Load("../../.env")
		if err != nil {
			log.Fatal("Error loading .env file")
		}
	}

	return &EnvConfig{
		Db: DBConfig{
			Host:     getEnv("DB_HOST", "localhost"),
			Port:     getEnv("DB_PORT", "5432"),
			Name:     getEnv("DB_NAME", "database"),
			User:     getEnv("DB_USER", "user"),
			Password: getEnv("DB_PASSWORD", "password"),
		},
		Server: ServerConfig{
			Port:    getEnv("SERVER_PORT", "3000"),
			Hosting: getEnv("SERVER_HOSTING", "localhost"),
		},
		Client: ClientConfig{
			Origin: getEnv("CLIENT_ORIGIN", "http://localhost:8080"),
			Domain: getEnv("CLIENT_DOMAIN", "localhost:8080"),
		},
		JWT: JWTConfig{
			Secret:         getEnv("JWT_SECRET", "secret"),
			ExpirationTime: getEnv("JWT_EXPIRATION_TIME", "24h"),
			CookieName:     getEnv("JWT_COOKIE_NAME", "access_token"),
		},
		BlobStorage: BlobStorageConfig{
			BaseDir: getEnv("BLOB_BASE_DIR", "storage"),
		},
	}
}
