package middleware

import (
	"errors"
	"fmt"
	"strings"

	"github.com/SneatX/phillips_go/internal/adapters/http/response"
	jwt "github.com/SneatX/phillips_go/internal/platform/token"
	appErrors "github.com/SneatX/phillips_go/internal/shared/errors"
	"github.com/gin-gonic/gin"
)

const (
	UserClaimsContextKey = "userClaims"
)

func JWTAuthMiddleware(jwtManager *jwt.JWTManager) gin.HandlerFunc {
	return func(c *gin.Context) {
		var accessToken string
		var tokenSource string

		cookie, err := c.<PERSON>(jwtManager.GetCookieName())
		if err == nil && cookie != "" {
			accessToken = cookie
			tokenSource = "cookie"
		} else {
			authHeader := c.<PERSON>eader("Authorization")
			if authHeader == "" {
				response.ErrorResponse(c, appErrors.NewUnauthorizedError("Authentication required"))
				c.Abort()
				return
			}

			tokenFields := strings.Fields(authHeader)
			if len(tokenFields) < 2 || tokenFields[0] != "Bearer" {
				response.ErrorResponse(c, appErrors.NewUnauthorizedError("Invalid Authorization header format. Must be 'Bearer <token>'"))
				c.Abort()
				return
			}
			accessToken = tokenFields[1]
			tokenSource = "header"
		}

		claims, err := jwtManager.VerifyToken(accessToken)
		if err != nil {
			var appErr appErrors.AppError
			if errors.As(err, &appErr) {
				response.ErrorResponse(c, appErr)
			} else {
				response.ErrorResponse(c, appErrors.NewUnauthorizedError("Failed to verify token", err))
			}
			c.Abort()
			return
		}

		// Store token source for potential logging or debugging
		c.Set("tokenSource", tokenSource)
		c.Set(UserClaimsContextKey, claims)
		c.Next()
	}
}

func GetUserClaimsFromContext(c *gin.Context) (*jwt.UserClaims, bool) {
	claims, exists := c.Get(UserClaimsContextKey)
	if !exists {
		return nil, false
	}
	userClaims, ok := claims.(*jwt.UserClaims)
	if !ok {
		return nil, false
	}
	return userClaims, true
}

func AuthorizeRole(requiredRoles ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		claims, exists := GetUserClaimsFromContext(c)
		if !exists || claims == nil {
			response.ErrorResponse(c, appErrors.NewUnauthorizedError("User claims not found in context. Authentication required."))
			c.Abort()
			return
		}

		userRole := claims.Role
		for _, requiredRole := range requiredRoles {
			if userRole == requiredRole {
				c.Next()
				return
			}
		}

		response.ErrorResponse(c, appErrors.NewUnauthorizedError(fmt.Sprintf("Access denied. User role '%s' is not authorized for this action.", userRole)))
		c.Abort()
	}
}
