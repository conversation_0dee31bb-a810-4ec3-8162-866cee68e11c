package routes

import (
	"github.com/SneatX/phillips_go/internal/adapters/http/handlers"
	"github.com/SneatX/phillips_go/internal/adapters/http/middleware"
	jwt "github.com/SneatX/phillips_go/internal/platform/token"
	"github.com/gin-gonic/gin"
)

func RegisterAdjusterRoutes(rg *gin.RouterGroup, handler *handlers.AdjusterHandler, jwtManager *jwt.JWTManager) {
	publicAdjusters := rg.Group("/adjusters")

	protectedAdjusters := publicAdjusters.Group("", middleware.JWTAuthMiddleware(jwtManager))
	{
		protectedAdjusters.GET("", handler.GetAllAdjusters)
		protectedAdjusters.GET("/:id", handler.GetAdjusterByID)

		protectedAdjusters.POST("", handler.CreateAdjuster)
		protectedAdjusters.PUT("/:id", handler.UpdateAdjuster)
	}
}
