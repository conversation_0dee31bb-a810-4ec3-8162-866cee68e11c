package database

import (
	"fmt"
	"log"

	"github.com/SneatX/phillips_go/internal/config"
	"github.com/SneatX/phillips_go/internal/core/domain"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
)

var DB *gorm.DB

func PostgresConnection(cfg *config.DBConfig) *gorm.DB {
	dsn := fmt.Sprintf("host=%s user=%s password=%s dbname=%s port=%s sslmode=disable", cfg.Host, cfg.User, cfg.Password, cfg.Name, cfg.Port)

	var err error
	if DB, err = gorm.Open(postgres.Open(dsn), &gorm.Config{}); err != nil {
		log.Fatal("Error in the db")
	}
	log.Println(" -------------- Db connected -------------- ")

	if err := DB.AutoMigrate(
		&domain.User{},
		&domain.Assignment{},
		&domain.Carrier{},
		&domain.Adjuster{},
		&domain.Contact{},
		&domain.Location{},
		&domain.Category{},
		&domain.Consultant{},
		&domain.AssignmentCategory{},
		&domain.Comment{},
		&domain.Rom{},
		&domain.Document{},
	); err != nil {
		log.Fatalf("AutoMigrate failed: %v", err)
	}
	return DB
}
