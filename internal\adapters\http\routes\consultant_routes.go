package routes

import (
	"github.com/SneatX/phillips_go/internal/adapters/http/handlers"
	"github.com/SneatX/phillips_go/internal/adapters/http/middleware"
	jwt "github.com/SneatX/phillips_go/internal/platform/token"
	"github.com/gin-gonic/gin"
)

func RegisterConsultantRoutes(rg *gin.RouterGroup, handler *handlers.ConsultantHandler, jwtManager *jwt.JWTManager) {
	publicConsultants := rg.Group("/consultants")

	protectedConsultants := publicConsultants.Group("", middleware.JWTAuthMiddleware(jwtManager))
	{
		protectedConsultants.GET("", handler.GetAllConsultants)
		protectedConsultants.GET("/:id", handler.GetConsultantByID)
		protectedConsultants.POST("", handler.CreateConsultant)
		protectedConsultants.PUT("/:id", handler.UpdateConsultant)
	}
}
