package routes

import (
	"github.com/SneatX/phillips_go/internal/adapters/http/handlers"
	"github.com/SneatX/phillips_go/internal/adapters/http/middleware"
	jwt "github.com/SneatX/phillips_go/internal/platform/token"
	"github.com/gin-gonic/gin"
)

func RegisterRomRoutes(rg *gin.RouterGroup, handler *handlers.RomHandler, jwtManager *jwt.JWTManager) {
	publicRoms := rg.Group("/roms")

	protectedRoms := publicRoms.Group("", middleware.JWTAuthMiddleware(jwtManager))
	{
		protectedRoms.POST("", handler.CreateRom)
		protectedRoms.PUT("/:id", handler.UpdateRom)
	}
}
