package handlers

import (
	"strconv"

	"github.com/SneatX/phillips_go/internal/adapters/http/response"
	"github.com/SneatX/phillips_go/internal/core/port/in"
	"github.com/SneatX/phillips_go/internal/dto"
	appErrors "github.com/SneatX/phillips_go/internal/shared/errors"
	"github.com/gin-gonic/gin"
)

type RomHandler struct {
	romService in.RomService
}

func NewRomHandler(romService in.RomService) *RomHandler {
	return &RomHandler{romService: romService}
}

func (h *RomHandler) CreateRom(c *gin.Context) {
	var req dto.CreateRomRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ErrorResponse(c, appErrors.NewValidationError("Invalid request body", err))
		return
	}

	romToCreate := dto.ToDomainRom(&req)
	createdRom, err := h.romService.Create(romToCreate)
	if err != nil {
		_ = c.Error(err)
		return
	}

	response.SuccessOne(c, dto.FromDomainRom(*createdRom))
}

func (h *RomHandler) UpdateRom(c *gin.Context) {
	romIDStr := c.Param("id")
	romID, err := strconv.ParseUint(romIDStr, 10, 32)
	if err != nil {
		response.ErrorResponse(c, appErrors.NewValidationError("Invalid rom ID", err))
		return
	}

	var req dto.UpdateRomRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ErrorResponse(c, appErrors.NewValidationError("Invalid request body", err))
		return
	}

	romToUpdate := dto.ToUpdateDomainRom(&req, uint(romID))
	updatedRom, err := h.romService.Update(romToUpdate)
	if err != nil {
		_ = c.Error(err)
		return
	}

	response.SuccessOne(c, dto.FromDomainRom(*updatedRom))
}
