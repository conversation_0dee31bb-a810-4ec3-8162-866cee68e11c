package http

import (
	"github.com/SneatX/phillips_go/internal/adapters/http/middleware"
	"github.com/SneatX/phillips_go/internal/adapters/http/routes"
	"github.com/SneatX/phillips_go/internal/config"
	"github.com/SneatX/phillips_go/internal/container"
	"github.com/gin-gonic/gin"
)

func SetUpServer(env *config.EnvConfig) *gin.Engine {
	appContainer := container.NewContainer(env)

	// Setup HTTP server
	router := gin.Default()
	router.RedirectTrailingSlash = false
	router.Use(middleware.ErrorHandler())
	router.Use(middleware.CORSMiddleware(env))

	// Register routes
	api := router.Group("/v1")
	routes.RegisterAllRoutes(
		api,
		appContainer.JWTManager,
		appContainer.UserHandler,
		appContainer.AuthHandler,
		appContainer.AssignmentHandler,
		appContainer.<PERSON><PERSON><PERSON><PERSON>,
		appContainer.<PERSON><PERSON><PERSON>ler,
		appContainer.<PERSON>ju<PERSON><PERSON><PERSON><PERSON>,
		appContainer.<PERSON><PERSON><PERSON><PERSON>,
		appContainer.<PERSON><PERSON><PERSON><PERSON>,
		appContainer.ConsultantHandler,
		appContainer.CommentHandler,
		appContainer.RomHandler,
		appContainer.DocumentHandler,
		appContainer.RoleHandler,
		appContainer.RomRoleHandler,
	)

	return router
}
