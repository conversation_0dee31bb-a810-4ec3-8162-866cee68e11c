package handlers

import (
	"strconv"

	"github.com/SneatX/phillips_go/internal/adapters/http/response"
	"github.com/SneatX/phillips_go/internal/core/port/in"
	"github.com/SneatX/phillips_go/internal/dto"
	appErrors "github.com/SneatX/phillips_go/internal/shared/errors"
	"github.com/gin-gonic/gin"
)

type CommentHandler struct {
	commentService in.CommentService
}

func NewCommentHandler(service in.CommentService) *CommentHandler {
	return &CommentHandler{commentService: service}
}

func (h *CommentHandler) GetCommentsByAssignmentID(c *gin.Context) {
	assignmentIDStr := c.Param("assignmentId")
	assignmentID, err := strconv.ParseUint(assignmentIDStr, 10, 32)
	if err != nil {
		response.ErrorResponse(c, appErrors.NewValidationError("Invalid assignment ID format. Must be a positive integer."))
		return
	}

	comments, err := h.commentService.GetByAssignmentID(uint(assignmentID))
	if err != nil {
		_ = c.Error(err)
		return
	}

	listResponse := dto.NewCommentListResponse(comments, "Comments retrieved successfully")
	response.SuccessMany(c, listResponse)
}

func (h *CommentHandler) GetCommentByID(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.ErrorResponse(c, appErrors.NewValidationError("Invalid comment ID format. Must be a positive integer."))
		return
	}

	comment, err := h.commentService.GetByID(uint(id))
	if err != nil {
		_ = c.Error(err)
		return
	}

	response.SuccessOne(c, dto.FromDomainComment(*comment))
}

func (h *CommentHandler) CreateComment(c *gin.Context) {
	var req dto.CreateCommentRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ErrorResponse(c, appErrors.NewValidationError("Invalid request body", err))
		return
	}

	commentToCreate := dto.ToDomainComment(&req)
	createdComment, err := h.commentService.Create(commentToCreate)
	if err != nil {
		_ = c.Error(err)
		return
	}

	response.SuccessOne(c, dto.FromDomainComment(*createdComment))
}

func (h *CommentHandler) UpdateComment(c *gin.Context) {
	commentIDStr := c.Param("id")
	commentID, err := strconv.ParseUint(commentIDStr, 10, 32)
	if err != nil {
		response.ErrorResponse(c, appErrors.NewValidationError("Invalid comment ID", err))
		return
	}

	var req dto.UpdateCommentRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ErrorResponse(c, appErrors.NewValidationError("Invalid request body", err))
		return
	}

	existingComment, err := h.commentService.GetByID(uint(commentID))
	if err != nil {
		_ = c.Error(err)
		return
	}

	commentToUpdate := dto.ToUpdateDomainComment(&req, uint(commentID), existingComment.ConsultantID)
	updatedComment, err := h.commentService.Update(commentToUpdate)
	if err != nil {
		_ = c.Error(err)
		return
	}

	response.SuccessOne(c, dto.FromDomainComment(*updatedComment))
}

func (h *CommentHandler) DeleteComment(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.ErrorResponse(c, appErrors.NewValidationError("Invalid comment ID format. Must be a positive integer."))
		return
	}

	err = h.commentService.Delete(uint(id))
	if err != nil {
		_ = c.Error(err)
		return
	}

	response.SuccessOne(c, gin.H{"message": "Comment deleted successfully"})
}
