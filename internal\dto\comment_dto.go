package dto

import (
	"time"

	"github.com/SneatX/phillips_go/internal/core/domain"
)

type CommentResponse struct {
	ID           uint                `json:"id"`
	AssignmentID uint                `json:"assignmentId"`
	ConsultantID uint                `json:"consultantId"`
	Consultant   *ConsultantResponse `json:"consultant,omitempty"`
	Content      string              `json:"content"`
	CreatedAt    time.Time           `json:"createdAt"`
	UpdatedAt    time.Time           `json:"updatedAt"`
}

func FromDomainComment(comment domain.Comment) CommentResponse {
	response := CommentResponse{
		ID:           comment.ID,
		AssignmentID: comment.AssignmentID,
		ConsultantID: comment.ConsultantID,
		Content:      comment.Content,
		CreatedAt:    comment.CreatedAt,
		UpdatedAt:    comment.UpdatedAt,
	}

	if comment.Consultant.ID != 0 {
		consultantResponse := FromDomainConsultant(comment.Consultant)
		response.Consultant = &consultantResponse
	}

	return response
}

type CommentListResponse struct {
	Results []CommentResponse `json:"results"`
	Total   int               `json:"total"`
	Message string            `json:"message"`
}

func NewCommentListResponse(comments []*domain.Comment, message string) CommentListResponse {
	commentsResponse := make([]CommentResponse, len(comments))
	for i, comment := range comments {
		commentsResponse[i] = FromDomainComment(*comment)
	}
	return CommentListResponse{
		Results: commentsResponse,
		Total:   len(comments),
		Message: message,
	}
}

type CreateCommentRequest struct {
	AssignmentID uint   `json:"assignmentId" binding:"required"`
	ConsultantID uint   `json:"consultantId" binding:"required"`
	Content      string `json:"content" binding:"required"`
}

func ToDomainComment(r *CreateCommentRequest) *domain.Comment {
	return &domain.Comment{
		AssignmentID: r.AssignmentID,
		ConsultantID: r.ConsultantID,
		Content:      r.Content,
	}
}

type UpdateCommentRequest struct {
	Content string `json:"content" binding:"required"`
}

func ToUpdateDomainComment(r *UpdateCommentRequest, commentID uint, consultantID uint) *domain.Comment {
	return &domain.Comment{
		ID:           commentID,
		ConsultantID: consultantID,
		Content:      r.Content,
	}
}
