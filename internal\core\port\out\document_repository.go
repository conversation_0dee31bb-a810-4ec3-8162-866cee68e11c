package out

import "github.com/SneatX/phillips_go/internal/core/domain"

type DocumentRepository interface {
	GetByID(id uint) (*domain.Document, error)
	GetByAssignmentID(assignmentID uint) ([]*domain.Document, error)
	GetUploadedByAssignmentID(assignmentID uint) ([]*domain.Document, error)
	CreateIntent(document *domain.Document) (*domain.Document, error)
	UpdateDocument(document *domain.Document) (*domain.Document, error)
}
