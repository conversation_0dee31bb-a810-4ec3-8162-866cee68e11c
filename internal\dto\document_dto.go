package dto

import (
	"time"

	"github.com/SneatX/phillips_go/internal/core/domain"
)

type DocumentResponse struct {
	ID           uint                `json:"id"`
	Assignment   *AssignmentResponse `json:"assignment"`
	AssignmentID uint                `json:"assignmentId"`
	FilePath     string              `json:"filePath"`
	FileName     string              `json:"fileName"`
	MimeType     string              `json:"mimeType"`
	Size         int64               `json:"size"`
	Status       string              `json:"status"`
	Sha256       string              `json:"sha256"`
	CreatedBy    *UserResponse       `json:"createdBy"`
	CreatedByID  uint                `json:"createdById"`
	CreatedAt    time.Time           `json:"createdAt"`
	UpdatedAt    time.Time           `json:"updatedAt"`
}

func FromDomainDocument(document domain.Document) DocumentResponse {
	response := DocumentResponse{
		ID:           document.ID,
		AssignmentID: document.AssignmentID,
		FilePath:     document.FilePath,
		FileName:     document.FileName,
		MimeType:     document.MimeType,
		Size:         document.Size,
		Status:       document.Status,
		Sha256:       document.Sha256,
		CreatedByID:  document.CreatedByID,
		CreatedAt:    document.CreatedAt,
		UpdatedAt:    document.UpdatedAt,
	}

	if document.CreatedBy.ID != 0 {
		user := FromDomainUser(document.CreatedBy)
		response.CreatedBy = &user
	}

	if document.Assignment.ID != 0 {
		assignment := FromDomainAssignment(document.Assignment)
		response.Assignment = &assignment
	}

	return response
}

type DocumentListResponse struct {
	Results []DocumentResponse `json:"results"`
	Total   int                `json:"total"`
	Message string             `json:"message"`
}

func NewDocumentListResponse(documents []*domain.Document, message string) DocumentListResponse {
	documentResponses := make([]DocumentResponse, len(documents))
	for i, document := range documents {
		documentResponses[i] = FromDomainDocument(*document)
	}
	return DocumentListResponse{
		Results: documentResponses,
		Total:   len(documents),
		Message: message,
	}
}

type CreateDocumentIntentRequest struct {
	AssignmentID uint   `json:"assignmentId" binding:"required"`
	FileName     string `json:"fileName" binding:"required"`
	MimeType     string `json:"mimeType" binding:"required"`
	Size         int64  `json:"size" binding:"required"`
}

func ToDomainDocumentIntent(r *CreateDocumentIntentRequest, createdByID uint) *domain.Document {
	return &domain.Document{
		AssignmentID: r.AssignmentID,
		FileName:     r.FileName,
		MimeType:     r.MimeType,
		Size:         r.Size,
		CreatedByID:  createdByID,
	}
}

type CreateDocumentIntentResponse struct {
	Document    DocumentResponse `json:"document"`
	UploadToken string           `json:"uploadToken"`
}
