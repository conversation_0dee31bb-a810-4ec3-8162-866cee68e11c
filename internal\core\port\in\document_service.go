package in

import (
	"io"

	"github.com/SneatX/phillips_go/internal/core/domain"
)

type DocumentService interface {
	GetByAssignmentID(assignmentID uint) ([]*domain.Document, error)
	GetUploadedByAssignmentID(assignmentID uint) ([]*domain.Document, error)
	CreateIntent(document *domain.Document) (*domain.Document, string, error)
	UploadDocument(token, originalName string, r io.Reader) (*domain.Document, error)
	PrepareAssignmentZip(assignmentID uint, docs []*domain.Document) (zipRelPath, zipFileName string, err error)
}
