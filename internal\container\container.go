package container

import (
	"log"
	"time"

	"github.com/SneatX/phillips_go/internal/adapters/blob"
	"github.com/SneatX/phillips_go/internal/adapters/http/handlers"
	"github.com/SneatX/phillips_go/internal/adapters/repository/postgres"
	"github.com/SneatX/phillips_go/internal/config"
	"github.com/SneatX/phillips_go/internal/core/port/in"
	"github.com/SneatX/phillips_go/internal/core/port/out"
	"github.com/SneatX/phillips_go/internal/core/service"
	"github.com/SneatX/phillips_go/internal/platform/database"
	jwt "github.com/SneatX/phillips_go/internal/platform/token"
	"gorm.io/gorm"
)

type Container struct {
	// Infrastructure
	DB          *gorm.DB
	JWTManager  *jwt.JWTManager
	BlobStorage out.BlobStorage

	// Repositories
	UserRepo       out.UserRepository
	AssignmentRepo out.AssignmentRepository
	LocationRepo   out.LocationRepository
	ContactRepo    out.ContactRepository
	AdjusterRepo   out.AdjusterRepository
	CategoryRepo   out.CategoryRepository
	CarrierRepo    out.CarrierRepository
	ConsultantRepo out.ConsultantRepository
	CommentRepo    out.CommentRepository
	RomRepo        out.RomRepository
	DocumentRepo   out.DocumentRepository

	// Services
	UserService       in.UserService
	AssignmentService in.AssignmentService
	LocationService   in.LocationService
	ContactService    in.ContactService
	AdjusterService   in.AdjusterService
	CategoryService   in.CategoryService
	CarrierService    in.CarrierService
	ConsultantService in.ConsultantService
	CommentService    in.CommentService
	RomService        in.RomService
	DocumentService   in.DocumentService

	// Handlers
	UserHandler       *handlers.UserHandler
	AuthHandler       *handlers.AuthHandler
	AssignmentHandler *handlers.AssignmentHandler
	LocationHandler   *handlers.LocationHandler
	ContactHandler    *handlers.ContactHandler
	AdjusterHandler   *handlers.AdjusterHandler
	CategoryHandler   *handlers.CategoryHandler
	CarrierHandler    *handlers.CarrierHandler
	ConsultantHandler *handlers.ConsultantHandler
	CommentHandler    *handlers.CommentHandler
	RomHandler        *handlers.RomHandler
	DocumentHandler   *handlers.DocumentHandler
}

func NewContainer(env *config.EnvConfig) *Container {
	container := &Container{}
	container.initInfrastructure(env)
	container.initRepositories()
	container.initServices(env)
	container.initHandlers(env)

	return container
}

func (c *Container) initInfrastructure(env *config.EnvConfig) {
	// Database
	c.DB = database.PostgresConnection(&env.Db)

	// JWT Manager
	jwtDuration, err := time.ParseDuration(env.JWT.ExpirationTime)
	if err != nil {
		log.Fatalf("Invalid JWT expiration time: %v", err)
	}
	c.JWTManager = jwt.NewJWTManager(env.JWT.Secret, jwtDuration, env.JWT.CookieName)

	// Blob Storage
	c.BlobStorage = blob.NewFSStorage(env.BlobStorage.BaseDir)
}

func (c *Container) initRepositories() {
	c.UserRepo = postgres.NewUserRepository(c.DB)
	c.AssignmentRepo = postgres.NewAssignmentRepository(c.DB)
	c.LocationRepo = postgres.NewLocationRepository(c.DB)
	c.ContactRepo = postgres.NewContactRepository(c.DB)
	c.AdjusterRepo = postgres.NewAdjusterRepository(c.DB)
	c.CategoryRepo = postgres.NewCategoryRepository(c.DB)
	c.CarrierRepo = postgres.NewCarrierRepository(c.DB)
	c.ConsultantRepo = postgres.NewConsultantRepository(c.DB)
	c.CommentRepo = postgres.NewCommentRepository(c.DB)
	c.RomRepo = postgres.NewRomRepository(c.DB)
	c.DocumentRepo = postgres.NewDocumentRepository(c.DB)
}

func (c *Container) initServices(env *config.EnvConfig) {
	c.UserService = service.NewUserService(c.UserRepo)
	c.AssignmentService = service.NewAssignmentService(c.AssignmentRepo, c.CategoryRepo)
	c.LocationService = service.NewLocationService(c.LocationRepo)
	c.ContactService = service.NewContactService(c.ContactRepo)
	c.AdjusterService = service.NewAdjusterService(c.AdjusterRepo)
	c.CategoryService = service.NewCategoryService(c.CategoryRepo)
	c.CarrierService = service.NewCarrierService(c.CarrierRepo)
	c.ConsultantService = service.NewConsultantService(c.ConsultantRepo)
	c.CommentService = service.NewCommentService(c.CommentRepo, c.AssignmentRepo, c.ConsultantRepo)
	c.RomService = service.NewRomService(c.RomRepo, c.AssignmentRepo)
	c.DocumentService = service.NewDocumentService(c.DocumentRepo, c.BlobStorage, c.JWTManager, env.BlobStorage.BaseDir)
}

func (c *Container) initHandlers(env *config.EnvConfig) {
	c.AuthHandler = handlers.NewAuthHandler(c.UserService, c.JWTManager, env)
	c.UserHandler = handlers.NewUserHandler(c.UserService, c.JWTManager)
	c.AssignmentHandler = handlers.NewAssignmentHandler(c.AssignmentService)
	c.LocationHandler = handlers.NewLocationHandler(c.LocationService)
	c.ContactHandler = handlers.NewContactHandler(c.ContactService)
	c.AdjusterHandler = handlers.NewAdjusterHandler(c.AdjusterService)
	c.CategoryHandler = handlers.NewCategoryHandler(c.CategoryService)
	c.CarrierHandler = handlers.NewCarrierHandler(c.CarrierService)
	c.ConsultantHandler = handlers.NewConsultantHandler(c.ConsultantService)
	c.CommentHandler = handlers.NewCommentHandler(c.CommentService)
	c.RomHandler = handlers.NewRomHandler(c.RomService)
	c.DocumentHandler = handlers.NewDocumentHandler(c.DocumentService, env.BlobStorage.BaseDir)
}

func (c *Container) Close() error {
	if c.DB != nil {
		sqlDB, err := c.DB.DB()
		if err != nil {
			return err
		}
		return sqlDB.Close()
	}
	return nil
}
