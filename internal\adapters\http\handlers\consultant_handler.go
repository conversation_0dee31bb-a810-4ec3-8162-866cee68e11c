package handlers

import (
	"strconv"

	"github.com/SneatX/phillips_go/internal/adapters/http/response"
	"github.com/SneatX/phillips_go/internal/core/port/in"
	"github.com/SneatX/phillips_go/internal/dto"
	appErrors "github.com/SneatX/phillips_go/internal/shared/errors"
	"github.com/gin-gonic/gin"
)

type ConsultantHandler struct {
	consultantService in.ConsultantService
}

func NewConsultantHandler(service in.ConsultantService) *ConsultantHandler {
	return &ConsultantHandler{consultantService: service}
}

func (h *ConsultantHandler) GetAllConsultants(c *gin.Context) {
	consultants, err := h.consultantService.GetAll()
	if err != nil {
		_ = c.Error(err)
		return
	}

	response.SuccessMany(c, dto.NewConsultantListResponse(consultants, "Consultants retrieved successfully"))
}

func (h *ConsultantHandler) GetConsultantByID(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.ErrorResponse(c, appErrors.NewValidationError("Invalid consultant ID format. Must be a positive integer."))
		return
	}

	consultant, err := h.consultantService.GetByID(uint(id))
	if err != nil {
		_ = c.Error(err)
		return
	}
	response.SuccessOne(c, dto.FromDomainConsultant(*consultant))
}

func (h *ConsultantHandler) CreateConsultant(c *gin.Context) {
	var req dto.CreateConsultantRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ErrorResponse(c, appErrors.NewValidationError("Invalid body request", err))
		return
	}

	consultantToCreate := dto.ToDomainConsultant(&req)
	createdConsultant, err := h.consultantService.Create(consultantToCreate)
	if err != nil {
		_ = c.Error(err)
		return
	}
	response.SuccessOne(c, dto.FromDomainConsultant(*createdConsultant))
}

func (h *ConsultantHandler) UpdateConsultant(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.ErrorResponse(c, appErrors.NewValidationError("Invalid consultant ID format. Must be a positive integer."))
		return
	}

	var req dto.UpdateConsultantRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ErrorResponse(c, appErrors.NewValidationError("Invalid request body", err))
		return
	}

	consultantToUpdate := dto.ToUpdateDomainConsultant(&req, uint(id))
	updatedConsultant, err := h.consultantService.Update(consultantToUpdate)
	if err != nil {
		_ = c.Error(err)
		return
	}

	response.SuccessOne(c, dto.FromDomainConsultant(*updatedConsultant))
}
