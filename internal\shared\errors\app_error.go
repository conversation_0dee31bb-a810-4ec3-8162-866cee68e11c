package errors

import (
	"fmt"
)

type AppError interface {
	Error() string
	Unwrap() error
	Code() string
	Type() ErrorType
	HTTPStatus() int
}

type ErrorType string

const (
	TypeNotFound           ErrorType = "NotFound"
	TypeValidation         ErrorType = "Validation"
	TypeConflict           ErrorType = "Conflict"
	TypeUnauthorized       ErrorType = "Unauthorized"
	TypeForbidden          ErrorType = "Forbidden"
	TypeInternal           ErrorType = "Internal"
	TypeServiceUnavailable ErrorType = "ServiceUnavailable"
)

type baseAppError struct {
	Msg     string
	ErrCode string
	ErrType ErrorType
	Status  int
	Wrapped error
}

func (e *baseAppError) Error() string {
	if e.Wrapped != nil {
		return fmt.Sprintf("%s: %s (wrapped: %v)", e.ErrType, e.Msg, e.Wrapped)
	}
	return fmt.Sprintf("%s: %s", e.ErrType, e.Msg)
}

func (e *baseAppError) Unwrap() error {
	return e.Wrapped
}

func (e *baseAppError) Code() string {
	return e.ErrCode
}

func (e *baseAppError) Type() ErrorType {
	return e.ErrType
}

func (e *baseAppError) HTTPStatus() int {
	return e.Status
}
