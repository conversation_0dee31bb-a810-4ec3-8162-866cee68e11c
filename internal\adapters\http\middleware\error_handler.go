package middleware

import (
	"errors"
	"log"
	"net/http"

	"github.com/SneatX/phillips_go/internal/adapters/http/response"
	appErrors "github.com/SneatX/phillips_go/internal/shared/errors"
	"github.com/gin-gonic/gin"
)

func ErrorHandler() gin.HandlerFunc {
	return func(c *gin.Context) {

		defer func() {
			if r := recover(); r != nil {
				log.Printf("PANIC RECOVERED: %v", r)
				c.<PERSON><PERSON><PERSON>(http.StatusInternalServerError, gin.H{
					"data": nil,
					"error": gin.H{
						"message": "An unexpected error occurred (panic)",
						"code":    "INTERNAL_SERVER_ERROR",
						"type":    appErrors.TypeInternal,
						"status":  500,
					},
				})
				c.Abort()
				return
			}
		}()

		c.Next()
		if len(c.Errors) > 0 {
			err := c.Errors[0].Err
			log.Printf("Request error: %v", err)

			var appErr appErrors.AppError
			if errors.As(err, &appErr) {
				response.ErrorResponse(c, appErr)
			} else {
				c.<PERSON>(http.StatusInternalServerError, gin.H{
					"data": nil,
					"error": gin.H{
						"message": "An unexpected server error occurred",
						"code":    "INTERNAL_SERVER_ERROR",
						"type":    appErrors.TypeInternal,
						"status":  500,
					},
				})
			}
			c.Abort()
		}
	}
}
