package service

import (
	"github.com/SneatX/phillips_go/internal/core/domain"
	"github.com/SneatX/phillips_go/internal/core/port/in"
	"github.com/SneatX/phillips_go/internal/core/port/out"
)

type adjusterService struct {
	adjusterRepo out.AdjusterRepository
}

func NewAdjusterService(repo out.AdjusterRepository) in.AdjusterService {
	return &adjusterService{adjusterRepo: repo}
}

func (s *adjusterService) GetAll() ([]*domain.Adjuster, error) {
	return s.adjusterRepo.GetAll()
}

func (s *adjusterService) GetByID(id uint) (*domain.Adjuster, error) {
	return s.adjusterRepo.GetByID(id)
}

func (s *adjusterService) Create(adjuster *domain.Adjuster) (*domain.Adjuster, error) {
	return s.adjusterRepo.Create(adjuster)
}

func (s *adjusterService) Update(adjuster *domain.Adjuster) (*domain.Adjuster, error) {
	return s.adjusterRepo.Update(adjuster)
}
