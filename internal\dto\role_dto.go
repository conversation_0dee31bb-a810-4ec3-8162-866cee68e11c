package dto

import (
	"time"

	"github.com/SneatX/phillips_go/internal/core/domain"
)

type RoleResponse struct {
	ID        uint      `json:"id"`
	Name      string    `json:"name"`
	Value     float64   `json:"value"`
	CreatedAt time.Time `json:"createdAt"`
	UpdatedAt time.Time `json:"updatedAt"`
}

func FromDomainRole(role domain.Role) RoleResponse {
	return RoleResponse{
		ID:        role.ID,
		Name:      role.Name,
		Value:     role.Value,
		CreatedAt: role.CreatedAt,
		UpdatedAt: role.UpdatedAt,
	}
}

type RoleListResponse struct {
	Results []RoleResponse `json:"results"`
	Total   int            `json:"total"`
	Message string         `json:"message"`
}

func NewRoleListResponse(roles []*domain.Role, message string) RoleListResponse {
	rolesResponse := make([]RoleResponse, len(roles))
	for i, role := range roles {
		rolesResponse[i] = FromDomainRole(*role)
	}
	return RoleListResponse{
		Results: rolesResponse,
		Total:   len(roles),
		Message: message,
	}
}

type CreateRoleRequest struct {
	Name  string  `json:"name" binding:"required"`
	Value float64 `json:"value" binding:"required"`
}

func ToDomainRole(r *CreateRoleRequest) *domain.Role {
	return &domain.Role{
		Name:  r.Name,
		Value: r.Value,
	}
}

type UpdateRoleRequest struct {
	Name  string  `json:"name" binding:"required"`
	Value float64 `json:"value" binding:"required"`
}

func ToUpdateDomainRole(r *UpdateRoleRequest, roleID uint) *domain.Role {
	return &domain.Role{
		ID:    roleID,
		Name:  r.Name,
		Value: r.Value,
	}
}
