package blob

import (
	"crypto/sha256"
	"encoding/hex"
	"io"
	"os"
	"path/filepath"
	"strings"
)

type FSStorage struct {
	BaseDir string
}

func NewFSStorage(base string) *FSStorage {
	return &FSStorage{BaseDir: strings.TrimRight(base, "/")}
}

func (s *FSStorage) Save(relativePath string, r io.Reader) (int64, string, error) {
	fullPath := filepath.Join(s.BaseDir, relativePath)

	if err := os.MkdirAll(filepath.Dir(fullPath), 0o750); err != nil {
		return 0, "", err
	}

	tmpPath := fullPath + ".part"
	f, err := os.Create(tmpPath)
	if err != nil {
		return 0, "", err
	}
	defer f.Close()

	h := sha256.New()
	n, err := io.Copy(io.MultiWriter(f, h), r)
	if err != nil {
		_ = os.Remove(tmpPath)
		return 0, "", err
	}

	if err := f.Close(); err != nil {
		_ = os.Remove(tmpPath)
		return 0, "", err
	}

	if err := os.Rename(tmpPath, fullPath); err != nil {
		_ = os.Remove(tmpPath)
		return 0, "", err
	}

	return n, hex.EncodeToString(h.Sum(nil)), nil
}
