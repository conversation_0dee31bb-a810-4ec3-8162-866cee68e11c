package errors

import "errors"

func NewValidationError(message string, originalErr ...error) AppError {
	var wrapped error
	if len(originalErr) > 0 {
		wrapped = originalErr[0]
	}
	return &baseAppError{
		Msg:     message,
		ErrCode: "INVALID_INPUT",
		ErrType: TypeValidation,
		Status:  400,
		Wrapped: wrapped,
	}
}

func IsValidationError(err error) bool {
	var appErr AppError
	return errors.As(err, &appErr) && appErr.Type() == TypeValidation
}
