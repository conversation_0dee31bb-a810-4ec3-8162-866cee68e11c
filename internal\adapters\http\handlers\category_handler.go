package handlers

import (
	"strconv"

	"github.com/SneatX/phillips_go/internal/adapters/http/response"
	"github.com/SneatX/phillips_go/internal/core/port/in"
	"github.com/SneatX/phillips_go/internal/dto"
	appErrors "github.com/SneatX/phillips_go/internal/shared/errors"

	"github.com/gin-gonic/gin"
)

type CategoryHandler struct {
	categoryService in.CategoryService
}

func NewCategoryHandler(service in.CategoryService) *CategoryHandler {
	return &CategoryHandler{categoryService: service}
}

func (h *CategoryHandler) GetAllCategories(c *gin.Context) {
	categories, err := h.categoryService.GetAll()
	if err != nil {
		_ = c.Error(err)
		return
	}

	listResponse := dto.NewCategoryListResponse(categories, "Categories retrieved successfully")
	response.SuccessMany(c, listResponse)
}

func (h *CategoryHandler) CreateCategory(c *gin.Context) {
	var req dto.CreateCategoryRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ErrorResponse(c, appErrors.NewValidationError("Invalid request body", err))
		return
	}

	categoryToCreate := dto.ToDomainCategory(&req)
	createdCategory, err := h.categoryService.Create(categoryToCreate)
	if err != nil {
		_ = c.Error(err)
		return
	}
	response.SuccessOne(c, dto.FromDomainCategory(*createdCategory))
}

func (h *CategoryHandler) UpdateCategory(c *gin.Context) {

	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.ErrorResponse(c, appErrors.NewValidationError("Invalid category ID format. Must be a positive integer."))
		return
	}

	var req dto.UpdateCategoryRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ErrorResponse(c, appErrors.NewValidationError("Invalid request body", err))
		return
	}

	categoryToUpdate := dto.ToUpdateDomainCategory(&req, uint(id))
	updatedCategory, err := h.categoryService.Update(categoryToUpdate)
	if err != nil {
		_ = c.Error(err)
		return
	}

	response.SuccessOne(c, dto.FromDomainCategory(*updatedCategory))

}

func (h *CategoryHandler) PatchAssignmentCategory(c *gin.Context) {
	var req dto.PatchAssignmentCategoryRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ErrorResponse(c, appErrors.NewValidationError("Invalid request body", err))
		return
	}

	assignmentCategoryToCreate := dto.ToPatchDomainAssignmentCategory(&req)
	createdAssignmentCategory, err := h.categoryService.PatchAssignmentCategory(assignmentCategoryToCreate)
	if err != nil {
		_ = c.Error(err)
		return
	}
	response.SuccessOne(c, dto.FromDomainAssignmentCategory(*createdAssignmentCategory))
}
