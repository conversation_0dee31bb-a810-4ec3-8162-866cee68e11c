package main

import (
	"fmt"
	"log"

	"github.com/SneatX/phillips_go/internal/adapters/http"
	"github.com/SneatX/phillips_go/internal/config"
)

func main() {
	env := config.LoadEnv()
	server := http.SetUpServer(env)

	addr := fmt.Sprintf("%s:%s", env.Server.Hosting, env.Server.Port)
	log.Printf("\n\nServer running at: http://%s\n\n", addr)

	if err := server.Run(addr); err != nil {
		log.Fatalf("Failed to start server: %v", err)
	}
}
