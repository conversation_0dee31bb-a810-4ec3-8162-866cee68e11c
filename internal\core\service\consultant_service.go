package service

import (
	"github.com/SneatX/phillips_go/internal/core/domain"
	"github.com/SneatX/phillips_go/internal/core/port/in"
	"github.com/SneatX/phillips_go/internal/core/port/out"
)

type consultantService struct {
	consultantRepo out.ConsultantRepository
}

func NewConsultantService(repo out.ConsultantRepository) in.ConsultantService {
	return &consultantService{consultantRepo: repo}
}

func (s *consultantService) GetAll() ([]*domain.Consultant, error) {
	return s.consultantRepo.GetAll()
}

func (s *consultantService) GetByID(id uint) (*domain.Consultant, error) {
	return s.consultantRepo.GetByID(id)
}

func (s *consultantService) <PERSON><PERSON>(consultant *domain.Consultant) (*domain.Consultant, error) {
	return s.consultantRepo.Create(consultant)
}

func (s *consultantService) Update(consultant *domain.Consultant) (*domain.Consultant, error) {
	return s.consultantRepo.Update(consultant)
}
