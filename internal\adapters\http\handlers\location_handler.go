package handlers

import (
	"strconv"

	"github.com/SneatX/phillips_go/internal/adapters/http/middleware"
	"github.com/SneatX/phillips_go/internal/adapters/http/response"
	"github.com/SneatX/phillips_go/internal/dto"
	appErrors "github.com/SneatX/phillips_go/internal/shared/errors"

	"github.com/SneatX/phillips_go/internal/core/port/in"
	"github.com/gin-gonic/gin"
)

type LocationHandler struct {
	locationService in.LocationService
}

func NewLocationHandler(service in.LocationService) *LocationHandler {
	return &LocationHandler{
		locationService: service,
	}
}

func (h *LocationHandler) CreateLocation(c *gin.Context) {
	_, exists := middleware.GetUserClaimsFromContext(c)
	if !exists {
		response.ErrorResponse(c, appErrors.NewUnauthorizedError("User claims not found in context. Authentication required."))
		return
	}

	var req dto.CreateLocationRequest
	if err := c.ShouldBindJ<PERSON>(&req); err != nil {
		response.ErrorResponse(c, appErrors.NewValidationError("Invalid request body", err))
		return
	}

	locationToCreate := dto.ToDomainLocation(&req)
	createdLocation, err := h.locationService.Create(locationToCreate)
	if err != nil {
		_ = c.Error(err)
		return
	}

	response.SuccessOne(c, dto.FromDomainLocation(*createdLocation))
}

func (h *LocationHandler) UpdateLocation(c *gin.Context) {
	_, exists := middleware.GetUserClaimsFromContext(c)
	if !exists {
		response.ErrorResponse(c, appErrors.NewUnauthorizedError("User claims not found in context. Authentication required."))
		return
	}

	// Get location ID from URL parameter
	locationIDStr := c.Param("id")
	locationID, err := strconv.ParseUint(locationIDStr, 10, 32)
	if err != nil {
		response.ErrorResponse(c, appErrors.NewValidationError("Invalid location ID", err))
		return
	}

	// Check if location exists
	_, err = h.locationService.GetByID(uint(locationID))
	if err != nil {
		_ = c.Error(err)
		return
	}

	var req dto.UpdateLocationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ErrorResponse(c, appErrors.NewValidationError("Invalid request body", err))
		return
	}

	locationToUpdate := dto.ToUpdateDomainLocation(&req, uint(locationID))
	updatedLocation, err := h.locationService.Update(locationToUpdate)
	if err != nil {
		_ = c.Error(err)
		return
	}

	response.SuccessOne(c, dto.FromDomainLocation(*updatedLocation))
}

func (h *LocationHandler) DeleteLocation(c *gin.Context) {
	_, exists := middleware.GetUserClaimsFromContext(c)
	if !exists {
		response.ErrorResponse(c, appErrors.NewUnauthorizedError("User claims not found in context. Authentication required."))
		return
	}

	// Get assignment ID from URL parameter (note: this is assignmentId, not location id)
	assignmentIDStr := c.Param("assignmentId")
	assignmentID, err := strconv.ParseUint(assignmentIDStr, 10, 32)
	if err != nil {
		response.ErrorResponse(c, appErrors.NewValidationError("Invalid assignment ID", err))
		return
	}

	// Note: This endpoint deletes location by assignment ID
	// In a real implementation, you'd need to get the location ID from the assignment
	// For now, we'll assume the assignment ID is the location ID
	// This should be improved to properly handle the relationship
	err = h.locationService.Delete(uint(assignmentID))
	if err != nil {
		_ = c.Error(err)
		return
	}

	response.SuccessOne(c, gin.H{"message": "Location deleted successfully"})
}
