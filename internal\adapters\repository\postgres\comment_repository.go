package postgres

import (
	"errors"
	"fmt"

	"github.com/SneatX/phillips_go/internal/core/domain"
	appErrors "github.com/SneatX/phillips_go/internal/shared/errors"
	"github.com/jackc/pgx/v5/pgconn"
	"gorm.io/gorm"
)

type commentRepository struct {
	DB *gorm.DB
}

func NewCommentRepository(DB *gorm.DB) *commentRepository {
	return &commentRepository{DB: DB}
}

func (r *commentRepository) GetByID(id uint) (*domain.Comment, error) {
	var comment domain.Comment

	if err := r.DB.
		Preload("Consultant").
		First(&comment, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, appErrors.NewNotFoundError(fmt.Sprintf("comment with ID %d could not be found", id), err)
		}
		return nil, appErrors.NewInternalError(fmt.Sprintf("failed to retrieve comment %d due to unexpected issue", id), err)
	}

	return &comment, nil
}

func (r *commentRepository) GetByAssignmentID(assignmentID uint) ([]*domain.Comment, error) {
	var comments []*domain.Comment

	if err := r.DB.
		Preload("Consultant").
		Where("assignment_id = ?", assignmentID).
		Order("created_at DESC").
		Find(&comments).Error; err != nil {
		return nil, appErrors.NewInternalError(fmt.Sprintf("failed to retrieve comments for assignment %d", assignmentID), err)
	}

	return comments, nil
}

func (r *commentRepository) Create(comment *domain.Comment) (*domain.Comment, error) {
	if result := r.DB.Create(comment); result.Error != nil {
		var pgErr *pgconn.PgError
		if errors.As(result.Error, &pgErr) {
			fmt.Printf("PostgreSQL error code: %s, message: %s\n", pgErr.Code, pgErr.Message)
			if pgErr.Code == "23503" {
				return nil, appErrors.NewNotFoundError("foreign key constraint violation, please check that the assignment and consultant exist before creating a comment")
			}
		}
		return nil, appErrors.NewInternalError("failed to create comment", result.Error)
	}

	if err := r.DB.
		Preload("Consultant").
		First(comment, comment.ID).Error; err != nil {
		return nil, appErrors.NewInternalError("failed to load created comment", err)
	}

	return comment, nil
}

func (r *commentRepository) Update(comment *domain.Comment) (*domain.Comment, error) {
	var existing domain.Comment

	if err := r.DB.First(&existing, comment.ID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, appErrors.NewNotFoundError("comment not found")
		}
		return nil, appErrors.NewInternalError("failed to find comment for update", err)
	}

	if err := r.DB.Model(&existing).Updates(map[string]interface{}{
		"content": comment.Content,
	}).Error; err != nil {
		var pgErr *pgconn.PgError
		if errors.As(err, &pgErr) {
			fmt.Printf("PostgreSQL error code: %s, message: %s\n", pgErr.Code, pgErr.Message)
			if pgErr.Code == "23503" {
				return nil, appErrors.NewNotFoundError("foreign key constraint violation, please check that the assignment and consultant exist")
			}
		}
		return nil, appErrors.NewInternalError("failed to update comment", err)
	}

	if err := r.DB.
		Preload("Consultant").
		First(&existing, existing.ID).Error; err != nil {
		return nil, appErrors.NewInternalError("failed to load updated comment", err)
	}

	return &existing, nil
}

func (r *commentRepository) Delete(id uint) error {
	var comment domain.Comment

	if err := r.DB.First(&comment, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return appErrors.NewNotFoundError(fmt.Sprintf("comment with ID %d could not be found", id), err)
		}
		return appErrors.NewInternalError(fmt.Sprintf("failed to find comment %d for deletion", id), err)
	}

	if err := r.DB.Delete(&comment).Error; err != nil {
		return appErrors.NewInternalError(fmt.Sprintf("failed to delete comment %d", id), err)
	}

	return nil
}
