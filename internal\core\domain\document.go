package domain

import "time"

type Document struct {
	ID           uint       `gorm:"primaryKey;autoIncrement" json:"id"`
	Assignment   Assignment `gorm:"constraint:OnUpdate:CASCADE,OnDelete:SET NULL;" json:"assignment"`
	AssignmentID uint       `gorm:"index" json:"assignmentId"`

	FilePath string `gorm:"size:512" json:"filePath"`
	FileName string `gorm:"size:255" json:"fileName"`
	MimeType string `gorm:"size:255" json:"mimeType"`
	Size     int64  `json:"size"`
	Status   string `gorm:"size:50" json:"status"`
	Sha256   string `gorm:"size:64" json:"sha256"`

	CreatedBy   User `gorm:"constraint:OnUpdate:CASCADE,OnDelete:SET NULL;" json:"createdBy"`
	CreatedByID uint `gorm:"index" json:"createdById"`

	CreatedAt time.Time `json:"createdAt"`
	UpdatedAt time.Time `json:"updatedAt"`
}
