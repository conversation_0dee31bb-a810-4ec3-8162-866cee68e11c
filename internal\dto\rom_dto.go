package dto

import (
	"time"

	"github.com/SneatX/phillips_go/internal/core/domain"
)

type RomResponse struct {
	ID           uint      `json:"id"`
	AssignmentID uint      `json:"assignmentId"`
	Area         float64   `json:"area"`
	Duration     float64   `json:"duration"`
	LabourRate   float64   `json:"labourRate"`
	MaterialRate float64   `json:"materialRate"`
	Contingency  float64   `json:"contingency"`
	CreatedAt    time.Time `json:"createdAt"`
	UpdatedAt    time.Time `json:"updatedAt"`
}

func FromDomainRom(rom domain.Rom) RomResponse {
	return RomResponse{
		ID:           rom.ID,
		AssignmentID: rom.AssignmentID,
		Area:         rom.Area,
		Duration:     rom.Duration,
		LabourRate:   rom.LabourRate,
		MaterialRate: rom.MaterialRate,
		Contingency:  rom.Contingency,
		CreatedAt:    rom.CreatedAt,
		UpdatedAt:    rom.UpdatedAt,
	}
}

type CreateRomRequest struct {
	AssignmentID uint    `json:"assignmentId" binding:"required"`
	Area         float64 `json:"area" binding:"required"`
	Duration     float64 `json:"duration" binding:"required"`
	LabourRate   float64 `json:"labourRate" binding:"required"`
	MaterialRate float64 `json:"materialRate" binding:"required"`
	Contingency  float64 `json:"contingency" binding:"required"`
}

func ToDomainRom(r *CreateRomRequest) *domain.Rom {
	return &domain.Rom{
		AssignmentID: r.AssignmentID,
		Area:         r.Area,
		Duration:     r.Duration,
		LabourRate:   r.LabourRate,
		MaterialRate: r.MaterialRate,
		Contingency:  r.Contingency,
	}
}

type UpdateRomRequest struct {
	AssignmentID uint    `json:"assignmentId" binding:"required"`
	Area         float64 `json:"area" binding:"required"`
	Duration     float64 `json:"duration" binding:"required"`
	LabourRate   float64 `json:"labourRate" binding:"required"`
	MaterialRate float64 `json:"materialRate" binding:"required"`
	Contingency  float64 `json:"contingency" binding:"required"`
}

func ToUpdateDomainRom(r *UpdateRomRequest, romID uint) *domain.Rom {
	return &domain.Rom{
		ID:           romID,
		Area:         r.Area,
		Duration:     r.Duration,
		LabourRate:   r.LabourRate,
		MaterialRate: r.MaterialRate,
		Contingency:  r.Contingency,
		AssignmentID: r.AssignmentID,
	}
}
