package domain

import "time"

type Rom struct {
	ID           uint    `gorm:"primaryKey" json:"id"`
	AssignmentID uint    `gorm:"index" json:"assignmentId"`
	Area         float64 `gorm:"type:decimal(5,2)" json:"area"`
	Duration     float64 `gorm:"type:decimal(5,2)" json:"duration"`
	LabourRate   float64 `gorm:"type:decimal(5,2)" json:"labourRate"`
	MaterialRate float64 `gorm:"type:decimal(5,2)" json:"materialRate"`
	Contingency  float64 `gorm:"type:decimal(5,2)" json:"contingency"`

	Roles []RomRole `gorm:"foreignKey:RomID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE;" json:"roles"`

	CreatedAt time.Time
	UpdatedAt time.Time
}
