package service

import (
	"github.com/SneatX/phillips_go/internal/core/domain"
	"github.com/SneatX/phillips_go/internal/core/port/in"
	"github.com/SneatX/phillips_go/internal/core/port/out"
	appErrors "github.com/SneatX/phillips_go/internal/shared/errors"
)

type commentService struct {
	commentRepo    out.CommentRepository
	assignmentRepo out.AssignmentRepository
	consultantRepo out.ConsultantRepository
}

func NewCommentService(
	commentRepo out.CommentRepository,
	assignmentRepo out.AssignmentRepository,
	consultantRepo out.ConsultantRepository,
) in.CommentService {
	return &commentService{
		commentRepo:    commentRepo,
		assignmentRepo: assignmentRepo,
		consultantRepo: consultantRepo,
	}
}

func (s *commentService) GetByID(id uint) (*domain.Comment, error) {
	return s.commentRepo.GetByID(id)
}

func (s *commentService) GetByAssignmentID(assignmentID uint) ([]*domain.Comment, error) {
	_, err := s.assignmentRepo.GetByID(assignmentID)
	if err != nil {
		return nil, err
	}

	return s.commentRepo.GetByAssignmentID(assignmentID)
}

func (s *commentService) Create(comment *domain.Comment) (*domain.Comment, error) {
	_, err := s.assignmentRepo.GetByID(comment.AssignmentID)
	if err != nil {
		return nil, appErrors.NewNotFoundError("assignment not found")
	}

	_, err = s.consultantRepo.GetByID(comment.ConsultantID)
	if err != nil {
		return nil, appErrors.NewNotFoundError("consultant not found")
	}

	return s.commentRepo.Create(comment)
}

func (s *commentService) Update(comment *domain.Comment) (*domain.Comment, error) {
	existingComment, err := s.commentRepo.GetByID(comment.ID)
	if err != nil {
		return nil, err
	}
	existingComment.Content = comment.Content

	return s.commentRepo.Update(existingComment)
}

func (s *commentService) Delete(id uint) error {
	_, err := s.commentRepo.GetByID(id)
	if err != nil {
		return err
	}

	return s.commentRepo.Delete(id)
}
