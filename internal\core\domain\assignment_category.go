package domain

import "time"

type AssignmentCategory struct {
	AssignmentID uint `gorm:"primaryKey;autoIncrement:false;uniqueIndex:idx_assignment_category" json:"assignmentId"`
	CategoryID   uint `gorm:"primaryKey;autoIncrement:false;uniqueIndex:idx_assignment_category" json:"categoryId"`

	PresentValue    float64 `gorm:"not null" json:"presentValue"`
	AsAnalyzedValue float64 `gorm:"not null" json:"asAnalyzedValue"`

	Assignment Assignment `gorm:"foreignKey:AssignmentID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE;"`
	Category   Category   `gorm:"foreignKey:CategoryID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE;"`

	CreatedAt time.Time `json:"createdAt"`
	UpdatedAt time.Time `json:"updatedAt"`
}
