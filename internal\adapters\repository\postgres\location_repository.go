package postgres

import (
	"errors"
	"fmt"

	"github.com/SneatX/phillips_go/internal/core/domain"
	appErrors "github.com/SneatX/phillips_go/internal/shared/errors"
	"gorm.io/gorm"
)

type locationRepository struct {
	DB *gorm.DB
}

func NewLocationRepository(DB *gorm.DB) *locationRepository {
	return &locationRepository{DB: DB}
}

func (r *locationRepository) GetByID(id uint) (*domain.Location, error) {
	var location domain.Location

	if err := r.DB.First(&location, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, appErrors.NewNotFoundError(fmt.Sprintf("location with ID %d could not be found", id), err)
		}
		return nil, appErrors.NewInternalError(fmt.Sprintf("failed to retrieve location %d due to unexpected issue", id), err)
	}

	return &location, nil
}

func (r *locationRepository) Create(location *domain.Location) (*domain.Location, error) {
	if result := r.DB.Create(&location); result.Error != nil {
		return nil, appErrors.NewInternalError("failed to create location", result.Error)
	}
	return location, nil
}

func (r *locationRepository) Update(location *domain.Location) (*domain.Location, error) {
	if result := r.DB.Save(&location); result.Error != nil {
		return nil, appErrors.NewInternalError("failed to update location", result.Error)
	}
	return location, nil
}

func (r *locationRepository) Delete(id uint) error {
	if result := r.DB.Delete(&domain.Location{}, id); result.Error != nil {
		return appErrors.NewInternalError("failed to delete location", result.Error)
	}
	return nil
}
