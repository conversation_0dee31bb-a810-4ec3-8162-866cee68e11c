package domain

import "time"

type RomRole struct {
	AssignmentID uint `gorm:"primaryKey;autoIncrement:false;uniqueIndex:idx_assignment_role" json:"assignmentId"`
	RoleID       uint `gorm:"primaryKey;autoIncrement:false;uniqueIndex:idx_assignment_role" json:"roleId"`

	Quantity float64 `gorm:"not null" json:"quantity"`

	Assignment Assignment `gorm:"foreignKey:AssignmentID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE;"`
	Role       Role       `gorm:"foreignKey:RoleID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE;"`

	CreatedAt time.Time `json:"createdAt"`
	UpdatedAt time.Time `json:"updatedAt"`
}
