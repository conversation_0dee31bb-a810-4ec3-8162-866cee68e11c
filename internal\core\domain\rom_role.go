package domain

import "time"

type RomRole struct {
	RomID  uint `gorm:"primaryKey;autoIncrement:false;uniqueIndex:idx_rom_role" json:"romId"`
	RoleID uint `gorm:"primaryKey;autoIncrement:false;uniqueIndex:idx_rom_role" json:"roleId"`

	Quantity float64 `gorm:"not null" json:"quantity"`

	Rom  Rom  `gorm:"foreignKey:RomID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE;"`
	Role Role `gorm:"foreignKey:RoleID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE;"`

	CreatedAt time.Time `json:"createdAt"`
	UpdatedAt time.Time `json:"updatedAt"`
}
