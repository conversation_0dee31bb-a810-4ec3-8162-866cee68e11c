package handlers

import (
	"strconv"

	"github.com/SneatX/phillips_go/internal/adapters/http/response"
	"github.com/SneatX/phillips_go/internal/core/port/in"
	"github.com/SneatX/phillips_go/internal/dto"
	appErrors "github.com/SneatX/phillips_go/internal/shared/errors"
	"github.com/gin-gonic/gin"
)

type AdjusterHandler struct {
	adjusterService in.AdjusterService
}

func NewAdjusterHandler(service in.AdjusterService) *AdjusterHandler {
	return &AdjusterHandler{adjusterService: service}
}

func (h *AdjusterHandler) GetAllAdjusters(c *gin.Context) {
	adjusters, err := h.adjusterService.GetAll()
	if err != nil {
		_ = c.Error(err)
		return
	}

	response.SuccessMany(c, dto.NewAdjusterListResponse(adjusters, "Adjusters retrieved successfully"))
}

func (h *AdjusterHandler) GetAdjusterByID(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.ErrorResponse(c, appErrors.NewValidationError("Invalid adjuster ID format. Must be a positive integer."))
		return
	}

	adjuster, err := h.adjusterService.GetByID(uint(id))
	if err != nil {
		_ = c.Error(err)
		return
	}
	response.SuccessOne(c, dto.FromDomainAdjuster(*adjuster))
}

func (h *AdjusterHandler) CreateAdjuster(c *gin.Context) {
	var req dto.CreateAdjusterRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ErrorResponse(c, appErrors.NewValidationError("Invalid body request", err))
		return
	}

	adjusterToCreate := dto.ToDomainAdjuster(&req)
	createdAdjuster, err := h.adjusterService.Create(adjusterToCreate)
	if err != nil {
		_ = c.Error(err)
		return
	}
	response.SuccessOne(c, dto.FromDomainAdjuster(*createdAdjuster))
}

func (h *AdjusterHandler) UpdateAdjuster(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.ErrorResponse(c, appErrors.NewValidationError("Invalid adjuster ID format. Must be a positive integer."))
		return
	}

	var req dto.UpdateAdjusterRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ErrorResponse(c, appErrors.NewValidationError("Invalid request body", err))
		return
	}

	adjusterToUpdate := dto.ToUpdateDomainAdjuster(&req, uint(id))
	updatedAdjuster, err := h.adjusterService.Update(adjusterToUpdate)
	if err != nil {
		_ = c.Error(err)
		return
	}

	response.SuccessOne(c, dto.FromDomainAdjuster(*updatedAdjuster))
}
