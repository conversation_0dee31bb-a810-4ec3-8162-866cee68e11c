package in

import "github.com/SneatX/phillips_go/internal/core/domain"

type RomRoleService interface {
	GetRolesByRomID(romID uint) ([]*domain.RomRole, error)
	GetRomsByRoleID(roleID uint) ([]*domain.RomRole, error)
	GetByRomAndRole(romID, roleID uint) (*domain.RomRole, error)
	AddRoleToRom(romRole *domain.RomRole) (*domain.RomRole, error)
	UpdateRoleQuantity(romRole *domain.RomRole) (*domain.RomRole, error)
	RemoveRoleFromRom(romID, roleID uint) error
	RemoveAllRolesFromRom(romID uint) error
}
