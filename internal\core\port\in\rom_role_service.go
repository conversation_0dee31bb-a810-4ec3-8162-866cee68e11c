package in

import "github.com/SneatX/phillips_go/internal/core/domain"

type RomRoleService interface {
	GetRolesByAssignmentID(assignmentID uint) ([]*domain.RomRole, error)
	GetAssignmentsByRoleID(roleID uint) ([]*domain.RomRole, error)
	GetByAssignmentAndRole(assignmentID, roleID uint) (*domain.RomRole, error)
	AddRoleToAssignment(romRole *domain.RomRole) (*domain.RomRole, error)
	UpdateRoleQuantity(romRole *domain.RomRole) (*domain.RomRole, error)
	RemoveRoleFromAssignment(assignmentID, roleID uint) error
	RemoveAllRolesFromAssignment(assignmentID uint) error
}
