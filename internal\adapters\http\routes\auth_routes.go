package routes

import (
	"github.com/SneatX/phillips_go/internal/adapters/http/handlers"
	"github.com/SneatX/phillips_go/internal/adapters/http/middleware"
	jwt "github.com/SneatX/phillips_go/internal/platform/token"
	"github.com/gin-gonic/gin"
)

func RegisterAuthRoutes(rg *gin.RouterGroup, jwtManager *jwt.JWTManager, handler *handlers.AuthHandler) {
	publicAuth := rg.Group("/auth")
	{
		publicAuth.POST("/login", handler.Login)
	}

	protectedAuth := publicAuth.Group("", middleware.JWTAuthMiddleware(jwtManager))
	{
		protectedAuth.POST("/logout", handler.Logout)
	}
}
