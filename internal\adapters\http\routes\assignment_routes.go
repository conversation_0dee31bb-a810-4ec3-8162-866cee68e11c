package routes

import (
	"github.com/SneatX/phillips_go/internal/adapters/http/handlers"
	"github.com/SneatX/phillips_go/internal/adapters/http/middleware"
	jwt "github.com/SneatX/phillips_go/internal/platform/token"
	"github.com/gin-gonic/gin"
)

func RegisterAssignmentRoutes(rg *gin.RouterGroup, handler *handlers.AssignmentHandler, jwtManager *jwt.JWTManager) {
	publicAssignments := rg.Group("/assignments")

	protectedAssignments := publicAssignments.Group("", middleware.JWTAuthMiddleware(jwtManager))
	{
		protectedAssignments.GET("", handler.GetAllAssignments)
		protectedAssignments.GET("/:id", handler.GetByID)
		protectedAssignments.GET("/me", handler.GetUserAssignments)

		protectedAssignments.POST("", handler.CreateAssignment)
		protectedAssignments.PUT("/:id", handler.UpdateAssignment)
	}
}
