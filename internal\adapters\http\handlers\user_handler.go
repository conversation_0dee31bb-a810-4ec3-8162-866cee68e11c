package handlers

import (
	"strconv"

	"github.com/SneatX/phillips_go/internal/adapters/http/middleware"
	"github.com/SneatX/phillips_go/internal/adapters/http/response"
	"github.com/SneatX/phillips_go/internal/core/port/in"
	"github.com/SneatX/phillips_go/internal/dto"
	jwt "github.com/SneatX/phillips_go/internal/platform/token"
	appErrors "github.com/SneatX/phillips_go/internal/shared/errors"

	"github.com/gin-gonic/gin"
)

type UserHandler struct {
	userService in.UserService
	jwtManager  *jwt.JWTManager
}

func NewUserHandler(service in.UserService, jwtManager *jwt.JWTManager) *UserHandler {
	return &UserHandler{
		userService: service,
		jwtManager:  jwtManager,
	}
}

func (h *UserHandler) GetMyProfile(c *gin.Context) {
	claims, exists := middleware.GetUserClaimsFromContext(c)
	if !exists {
		response.ErrorResponse(c, appErrors.NewUnauthorizedError("User claims not found in context. Authentication required."))
		return
	}

	user, err := h.userService.GetCurrentUser(claims.UserID)
	if err != nil {
		_ = c.Error(err)
		return
	}

	response.SuccessOne(c, dto.FromDomainUser(*user))
}

func (h *UserHandler) GetUserByID(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.ErrorResponse(c, appErrors.NewValidationError("Invalid user ID format. Must be a positive integer."))
		return
	}

	user, err := h.userService.GetByID(uint(id))
	if err != nil {
		_ = c.Error(err)
		return
	}

	response.SuccessOne(c, dto.FromDomainUser(*user))
}

func (h *UserHandler) GetAllUsers(c *gin.Context) {
	users, err := h.userService.GetAll()
	if err != nil {
		_ = c.Error(err)
		return
	}
	listResponse := dto.NewUsersListResponse(users, "Users retrieved successfully.")
	response.SuccessMany(c, listResponse)
}

func (h *UserHandler) CreateUser(c *gin.Context) {
	var req dto.UserCreateRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		response.ErrorResponse(c, appErrors.NewValidationError("Invalid request payload: "+err.Error()))
		return
	}

	userDomain := req.ToDomainUser()
	createdUser, err := h.userService.Create(userDomain)

	if err != nil {
		_ = c.Error(err)
		return
	}

	token, err := h.jwtManager.GenerateToken(createdUser.ID, createdUser.Email, createdUser.Role)
	if err != nil {
		_ = c.Error(err)
		return
	}
	c.SetCookie(
		h.jwtManager.GetCookieName(),
		token,
		int(h.jwtManager.GetTokenDuration().Seconds()),
		"/",
		"",
		false,
		true,
	)

	response.SuccessOne(c, dto.FromDomainUser(*createdUser))
}
