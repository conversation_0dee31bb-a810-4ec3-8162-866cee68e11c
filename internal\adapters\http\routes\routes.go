package routes

import (
	"github.com/SneatX/phillips_go/internal/adapters/http/handlers"
	jwt "github.com/SneatX/phillips_go/internal/platform/token"
	"github.com/gin-gonic/gin"
)

func RegisterAllRoutes(
	api *gin.RouterGroup,
	jwtManager *jwt.JWTManager,
	userHandler *handlers.UserHandler,
	authHandler *handlers.AuthHandler,
	assignmentHandler *handlers.AssignmentHandler,
	locationHandler *handlers.LocationHandler,
	contactHandler *handlers.ContactHandler,
	adjusterHandler *handlers.AdjusterHandler,
	categoryHandler *handlers.CategoryHandler,
	carrierHandler *handlers.<PERSON><PERSON><PERSON><PERSON>,
	consultantHandler *handlers.Consultant<PERSON>andler,
	commentHandler *handlers.CommentHandler,
	romHandler *handlers.RomHandler,
	documentHandler *handlers.DocumentHandler,
	roleHandler *handlers.<PERSON><PERSON>and<PERSON>,
	romRoleHandler *handlers.RomRoleHandler,
) {
	RegisterAuthRoutes(api, jwtManager, authHandler)
	RegisterUserRoutes(api, jwtManager, userHandler)
	RegisterAssignmentRoutes(api, assignmentHandler, jwtManager)
	RegisterRoleRoutes(api, roleHandler, jwtManager)
	RegisterRomRoleRoutes(api, romRoleHandler, jwtManager)
	RegisterLocationRoutes(api, locationHandler, jwtManager)
	RegisterContactRoutes(api, contactHandler, jwtManager)
	RegisterAdjusterRoutes(api, adjusterHandler, jwtManager)
	RegisterCategoryRoutes(api, categoryHandler, jwtManager)
	RegisterCarrierRoutes(api, carrierHandler, jwtManager)
	RegisterConsultantRoutes(api, consultantHandler, jwtManager)
	RegisterCommentRoutes(api, commentHandler, jwtManager)
	RegisterRomRoutes(api, romHandler, jwtManager)
	RegisterDocumentRoutes(api, documentHandler, jwtManager)
}
