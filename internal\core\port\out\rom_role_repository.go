package out

import "github.com/SneatX/phillips_go/internal/core/domain"

type RomRoleRepository interface {
	GetByRomID(romID uint) ([]*domain.RomRole, error)
	GetByRoleID(roleID uint) ([]*domain.RomRole, error)
	GetByRomAndRole(romID, roleID uint) (*domain.RomRole, error)
	Create(romRole *domain.RomRole) (*domain.RomRole, error)
	Update(romRole *domain.RomRole) (*domain.RomRole, error)
	Delete(romID, roleID uint) error
	DeleteByRomID(romID uint) error
}
