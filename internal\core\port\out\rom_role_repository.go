package out

import "github.com/SneatX/phillips_go/internal/core/domain"

type RomRoleRepository interface {
	GetByAssignmentID(assignmentID uint) ([]*domain.RomRole, error)
	GetByRoleID(roleID uint) ([]*domain.RomRole, error)
	GetByAssignmentAndRole(assignmentID, roleID uint) (*domain.RomRole, error)
	Create(romRole *domain.RomRole) (*domain.RomRole, error)
	Update(romRole *domain.RomRole) (*domain.RomRole, error)
	Delete(assignmentID, roleID uint) error
	DeleteByAssignmentID(assignmentID uint) error
}
