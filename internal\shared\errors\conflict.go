package errors

import "errors"

func NewConflictError(message string, originalErr ...error) AppError {
	var wrapped error
	if len(originalErr) > 0 {
		wrapped = originalErr[0]
	}
	return &baseAppError{
		Msg:     message,
		ErrCode: "RESOURCE_CONFLICT",
		ErrType: TypeConflict,
		Status:  409,
		Wrapped: wrapped,
	}
}

func IsConflictError(err error) bool {
	var appErr AppError
	return errors.As(err, &appErr) && appErr.Type() == TypeConflict
}
