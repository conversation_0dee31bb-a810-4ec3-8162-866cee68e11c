package routes

import (
	"github.com/SneatX/phillips_go/internal/adapters/http/handlers"
	"github.com/SneatX/phillips_go/internal/adapters/http/middleware"
	jwt "github.com/SneatX/phillips_go/internal/platform/token"
	"github.com/gin-gonic/gin"
)

func RegisterLocationRoutes(rg *gin.RouterGroup, handler *handlers.LocationHandler, jwtManager *jwt.JWTManager) {
	publicLocations := rg.Group("/locations")

	protectedLocations := publicLocations.Group("", middleware.JWTAuthMiddleware(jwtManager))
	{
		protectedLocations.POST("", handler.CreateLocation)
		protectedLocations.PUT("/:id", handler.UpdateLocation)
		protectedLocations.DELETE("/:assignmentId", handler.DeleteLocation)
	}
}
