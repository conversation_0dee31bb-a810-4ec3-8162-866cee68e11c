# 📦 Phillips Go API

A robust backend application for managing assignments and users, meticulously crafted in Go. This API is built upon a clean, modular, and scalable architecture, designed to support an insurance consulting company in managing projects, clients, adjusters, and claims-related data.

---

## 🧱 Architecture

This project adheres to a Clean Architecture + Hexagonal Architecture approach, ensuring clear decoupling between domain, application, and external framework layers. This design promotes maintainability, testability, and flexibility.

```bash
phillips-go/
├── cmd/                  # Application entry points
│   └── api/              # Starts the HTTP server and wires up dependencies.
├── internal/             # All project-specific, non-exportable code.
│   ├── core/             # **Business Core:** Domain and application logic, independent of external frameworks.
│   │   ├── domain/       # Core business entities.
│   │   ├── port/         # Interfaces defining contracts between layers.
│   │   │   ├── in/       # Inbound ports: what the application exposes.
│   │   │   └── out/      # Outbound ports: what the application needs from external systems.
│   │   └── service/      # Business use case implementations.
│   ├── adapters/         # Implementations that connect the core to external systems.
│   │   ├── http/         # HTTP-specific components.
│   │   │   ├── handlers/    # HTTP request handlers.
│   │   │   ├── middleware/  # Global HTTP middlewares (e.g., error handling).
│   │   │   ├── response/    # Standardized HTTP response helpers.
│   │   │   └── routes/      # HTTP route definitions.
│   │   └── repository/   # Database/persistence implementations.
│   │       └── postgres/ # PostgreSQL-specific repository implementation.
│   ├── dto/              # Data Transfer Objects for API request/response contracts.
│   ├── validation/       # Custom business validation logic.
│   ├── platform/         # External tool/framework-specific implementations.
│   │   ├── config/       # Application configuration loading.
│   │   ├── database/     # Database connection and setup.
│   │   ├── jwt/          # JWT token handling.
│   │   └── logger/       # Application logging setup.
│   └── shared/           # Common utilities used across layers.
│       └── errors/       # Custom application error definitions.
├── pkg/                  # Generic, reusable utilities (could be separate Go modules).
├── scripts/              # Auxiliary scripts (e.g., database migrations).
├── go.mod                # Go module definition and direct dependencies.
└── go.sum                # Checksums for module dependencies.
└── README.md             # Project documentation.
```

---

## 🧪 Technologies & Tools

| Tecnología                 | Descripción                                   |
| -------------------------- | --------------------------------------------- |
| **Go**                     | Lenguaje principal (v1.21+)                   |
| **Gin**                    | Framework HTTP ligero y rápido                |
| **GORM**                   | ORM para interacción con PostgreSQL           |
| **Docker**                 | Contenedor para levantar base de datos local  |
| **Air**                    | Recarga automática en desarrollo              |
| **Clean Architecture**     | Separación en capas para mejor mantenibilidad |
| **Hexagonal Architecture** | Permite desacoplar puertos y adaptadores      |

---

## 🗄️ Database (PostgreSQL with Docker)

For local development, the PostgreSQL database runs within a Docker container.

## Create database

```bash
docker compose up -d
```

- **Container**: phillips_db
- **DB**: phillipsdb
- **User**: phillips
- **Password**: phillipspassword
- **Port**: 5432

### Connect

```bash
docker exec -it phillips_db psql -U phillips -d phillipsdb
```

## 🚀 Getting Started Locally

1. ### Clone the Repository

```bash
    git clone https://github.com/SneatX/phillips_go.git
    cd phillips_go
```

2. ### Install Dependencies

```bash
    go mod tidy
```

3. ### Start the Database (if not already running)

```bash
    docker start phillips_db
```

## 🧠 Autor

Santiago Alexander Ospina
Software Developer & Engineer in Training
