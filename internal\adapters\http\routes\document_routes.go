package routes

import (
	"github.com/SneatX/phillips_go/internal/adapters/http/handlers"
	"github.com/SneatX/phillips_go/internal/adapters/http/middleware"
	jwt "github.com/SneatX/phillips_go/internal/platform/token"
	"github.com/gin-gonic/gin"
)

func RegisterDocumentRoutes(rg *gin.RouterGroup, handler *handlers.DocumentHandler, jwtManager *jwt.JWTManager) {
	publicDocuments := rg.Group("/documents")

	protectedDocuments := publicDocuments.Group("", middleware.JWTAuthMiddleware(jwtManager))
	{
		protectedDocuments.GET("/:assignmentId", handler.GetDocumentsByAssignmentID)
		protectedDocuments.GET("/:assignmentId/bundle", handler.DownloadAssignmentBundle)
		protectedDocuments.POST("", handler.CreateDocumentIntent)
		protectedDocuments.POST("/upload", handler.CreateDocumentUpload)
	}
}
