package handlers

import (
	"github.com/SneatX/phillips_go/internal/adapters/http/middleware"
	"github.com/SneatX/phillips_go/internal/adapters/http/response"
	"github.com/SneatX/phillips_go/internal/config"
	"github.com/SneatX/phillips_go/internal/core/port/in"
	"github.com/SneatX/phillips_go/internal/dto"
	jwt "github.com/SneatX/phillips_go/internal/platform/token"
	appErrors "github.com/SneatX/phillips_go/internal/shared/errors"

	"github.com/gin-gonic/gin"
)

type AuthHandler struct {
	userService in.UserService
	jwtManager  *jwt.JWTManager
	env         *config.EnvConfig
}

func NewAuthHandler(service in.UserService, jwtManager *jwt.JWTManager, env *config.EnvConfig) *AuthHandler {
	return &AuthHandler{
		userService: service,
		jwtManager:  jwtManager,
		env:         env,
	}
}

func (h *AuthHandler) Login(c *gin.Context) {
	var req dto.LoginRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		response.ErrorResponse(c, appErrors.NewValidationError("Invalid request payload: "+err.Error()))
		return
	}

	user, err := h.userService.Authenticate(req.Email, req.Password)
	if err != nil {
		_ = c.Error(err)
		return
	}

	token, err := h.jwtManager.GenerateToken(user.ID, user.Email, user.Role)
	if err != nil {
		_ = c.Error(err)
		return
	}
	cookerName := h.jwtManager.GetCookieName()
	cookieDuration := int(h.jwtManager.GetTokenDuration().Seconds())
	c.SetCookie(
		cookerName,
		token,
		cookieDuration,
		"/",
		h.env.Client.Domain,
		false,
		true,
	)

	loginResponse := dto.NewLoginResponse(*user)
	response.SuccessOne(c, loginResponse)
}

func (h *AuthHandler) Logout(c *gin.Context) {
	_, exists := middleware.GetUserClaimsFromContext(c)
	if !exists {
		response.ErrorResponse(c, appErrors.NewUnauthorizedError("User claims not found in context. Authentication required."))
		return
	}

	c.SetCookie(
		h.jwtManager.GetCookieName(),
		"",
		-1,
		"/",
		"",
		false,
		true,
	)

	logoutResponse := dto.NewLogoutResponse()
	response.SuccessOne(c, logoutResponse)
}
