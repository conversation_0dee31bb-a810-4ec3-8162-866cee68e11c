package dto

import (
	"time"

	"github.com/SneatX/phillips_go/internal/core/domain"
)

type ConsultantResponse struct {
	ID        uint      `json:"id"`
	Name      string    `json:"name"`
	Email     string    `json:"email"`
	Phone     string    `json:"phone"`
	CreatedAt time.Time `json:"createdAt"`
	UpdatedAt time.Time `json:"updatedAt"`
}

func FromDomainConsultant(consultant domain.Consultant) ConsultantResponse {
	return ConsultantResponse{
		ID:        consultant.ID,
		Name:      consultant.Name,
		Email:     consultant.Email,
		Phone:     consultant.Phone,
		CreatedAt: consultant.CreatedAt,
		UpdatedAt: consultant.UpdatedAt,
	}
}

type ConsultantListResponse struct {
	Results []ConsultantResponse `json:"results"`
	Total   int                  `json:"total"`
	Message string               `json:"message"`
}

func NewConsultantListResponse(consultants []*domain.Consultant, message string) ConsultantListResponse {
	consultantsResponse := make([]ConsultantResponse, len(consultants))

	for i, consultant := range consultants {
		consultantsResponse[i] = FromDomainConsultant(*consultant)
	}
	return ConsultantListResponse{
		Results: consultantsResponse,
		Total:   len(consultantsResponse),
		Message: message,
	}
}

type CreateConsultantRequest struct {
	Name  string `json:"name" binding:"required"`
	Email string `json:"email"`
	Phone string `json:"phone"`
}

func ToDomainConsultant(r *CreateConsultantRequest) *domain.Consultant {
	return &domain.Consultant{
		Name:  r.Name,
		Email: r.Email,
		Phone: r.Phone,
	}
}

type UpdateConsultantRequest struct {
	Name  string `json:"name"`
	Email string `json:"email"`
	Phone string `json:"phone"`
}

func ToUpdateDomainConsultant(r *UpdateConsultantRequest, consultantID uint) *domain.Consultant {
	return &domain.Consultant{
		ID:    consultantID,
		Name:  r.Name,
		Email: r.Email,
		Phone: r.Phone,
	}
}
