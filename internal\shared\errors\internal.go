package errors

import "errors"

func NewInternalError(message string, originalErr ...error) AppError {
	var wrapped error
	if len(originalErr) > 0 {
		wrapped = originalErr[0]
	}
	return &baseAppError{
		Msg:     message,
		ErrCode: "INTERNAL_SERVER_ERROR",
		ErrType: TypeInternal,
		Status:  500,
		Wrapped: wrapped,
	}
}

func IsInternalError(err error) bool {
	var appErr AppError
	return errors.As(err, &appErr) && appErr.Type() == TypeInternal
}
