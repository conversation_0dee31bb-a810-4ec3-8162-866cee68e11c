package service

import (
	"github.com/SneatX/phillips_go/internal/core/domain"
	"github.com/SneatX/phillips_go/internal/core/port/in"
	"github.com/SneatX/phillips_go/internal/core/port/out"
)

type romRoleService struct {
	romRoleRepo out.RomRoleRepository
	romRepo     out.RomRepository
	roleRepo    out.RoleRepository
}

func NewRomRoleService(romRoleRepo out.RomRoleRepository, romRepo out.RomRepository, roleRepo out.RoleRepository) in.RomRoleService {
	return &romRoleService{
		romRoleRepo: romRoleRepo,
		romRepo:     romRepo,
		roleRepo:    roleRepo,
	}
}

func (s *romRoleService) GetRolesByRomID(romID uint) ([]*domain.RomRole, error) {
	return s.romRoleRepo.GetByRomID(romID)
}

func (s *romRoleService) GetRomsByRoleID(roleID uint) ([]*domain.RomRole, error) {
	// Verify role exists
	_, err := s.roleRepo.GetByID(roleID)
	if err != nil {
		return nil, err
	}

	return s.romRoleRepo.GetByRoleID(roleID)
}

func (s *romRoleService) GetByRomAndRole(romID, roleID uint) (*domain.RomRole, error) {
	return s.romRoleRepo.GetByRomAndRole(romID, roleID)
}

func (s *romRoleService) AddRoleToRom(romRole *domain.RomRole) (*domain.RomRole, error) {
	// Verify role exists
	_, err := s.roleRepo.GetByID(romRole.RoleID)
	if err != nil {
		return nil, err
	}

	return s.romRoleRepo.Create(romRole)
}

func (s *romRoleService) UpdateRoleQuantity(romRole *domain.RomRole) (*domain.RomRole, error) {
	// Verify the role assignment exists
	_, err := s.romRoleRepo.GetByRomAndRole(romRole.RomID, romRole.RoleID)
	if err != nil {
		return nil, err
	}

	return s.romRoleRepo.Update(romRole)
}

func (s *romRoleService) RemoveRoleFromRom(romID, roleID uint) error {
	return s.romRoleRepo.Delete(romID, roleID)
}

func (s *romRoleService) RemoveAllRolesFromRom(romID uint) error {
	return s.romRoleRepo.DeleteByRomID(romID)
}
