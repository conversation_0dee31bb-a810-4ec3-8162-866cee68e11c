package service

import (
	"github.com/SneatX/phillips_go/internal/core/domain"
	"github.com/SneatX/phillips_go/internal/core/port/in"
	"github.com/SneatX/phillips_go/internal/core/port/out"
)

type romRoleService struct {
	romRoleRepo    out.RomRoleRepository
	assignmentRepo out.AssignmentRepository
	roleRepo       out.RoleRepository
}

func NewRomRoleService(romRoleRepo out.RomRoleRepository, assignmentRepo out.AssignmentRepository, roleRepo out.RoleRepository) in.RomRoleService {
	return &romRoleService{
		romRoleRepo:    romRoleRepo,
		assignmentRepo: assignmentRepo,
		roleRepo:       roleRepo,
	}
}

func (s *romRoleService) GetRolesByAssignmentID(assignmentID uint) ([]*domain.RomRole, error) {
	// Verify assignment exists
	_, err := s.assignmentRepo.GetByID(assignmentID)
	if err != nil {
		return nil, err
	}

	return s.romRoleRepo.GetByAssignmentID(assignmentID)
}

func (s *romRoleService) GetAssignmentsByRoleID(roleID uint) ([]*domain.RomRole, error) {
	// Verify role exists
	_, err := s.roleRepo.GetByID(roleID)
	if err != nil {
		return nil, err
	}

	return s.romRoleRepo.GetByRoleID(roleID)
}

func (s *romRoleService) GetByAssignmentAndRole(assignmentID, roleID uint) (*domain.RomRole, error) {
	return s.romRoleRepo.GetByAssignmentAndRole(assignmentID, roleID)
}

func (s *romRoleService) AddRoleToAssignment(romRole *domain.RomRole) (*domain.RomRole, error) {
	// Verify assignment exists
	_, err := s.assignmentRepo.GetByID(romRole.AssignmentID)
	if err != nil {
		return nil, err
	}

	// Verify role exists
	_, err = s.roleRepo.GetByID(romRole.RoleID)
	if err != nil {
		return nil, err
	}

	return s.romRoleRepo.Create(romRole)
}

func (s *romRoleService) UpdateRoleQuantity(romRole *domain.RomRole) (*domain.RomRole, error) {
	// Verify the role assignment exists
	_, err := s.romRoleRepo.GetByAssignmentAndRole(romRole.AssignmentID, romRole.RoleID)
	if err != nil {
		return nil, err
	}

	return s.romRoleRepo.Update(romRole)
}

func (s *romRoleService) RemoveRoleFromAssignment(assignmentID, roleID uint) error {
	return s.romRoleRepo.Delete(assignmentID, roleID)
}

func (s *romRoleService) RemoveAllRolesFromAssignment(assignmentID uint) error {
	// Verify assignment exists
	_, err := s.assignmentRepo.GetByID(assignmentID)
	if err != nil {
		return err
	}

	return s.romRoleRepo.DeleteByAssignmentID(assignmentID)
}
