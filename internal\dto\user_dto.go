package dto

import (
	"time"

	"github.com/SneatX/phillips_go/internal/core/domain"
)

type UserCreateRequest struct {
	Name     string `json:"name" binding:"required"`
	Email    string `json:"email" binding:"required,email"`
	Password string `json:"password" binding:"required,min=8"`
	Role     string `json:"role" binding:"required,oneof=admin user"`
}

func (r *UserCreateRequest) ToDomainUser() domain.User {
	return domain.User{
		Name:     r.Name,
		Email:    r.Email,
		Password: r.Password,
		Role:     r.Role,
	}
}

type UserResponse struct {
	ID        uint      `json:"id"`
	Name      string    `json:"name"`
	Email     string    `json:"email"`
	Role      string    `json:"role"`
	CreatedAt time.Time `json:"createdAt"`
	UpdatedAt time.Time `json:"updatedAt"`
}

func FromDomainUser(user domain.User) UserResponse {
	return UserResponse{
		ID:        user.ID,
		Name:      user.Name,
		Email:     user.Email,
		Role:      user.Role,
		CreatedAt: user.CreatedAt,
		UpdatedAt: user.UpdatedAt,
	}
}

type UsersListResponse struct {
	Results []UserResponse `json:"results"`
	Total   int            `json:"total"`
	Message string         `json:"message"`
}

func NewUsersListResponse(users []domain.User, message string) UsersListResponse {
	userResponses := make([]UserResponse, len(users))
	for i, user := range users {
		userResponses[i] = FromDomainUser(user)
	}
	return UsersListResponse{
		Results: userResponses,
		Total:   len(users),
		Message: message,
	}
}
