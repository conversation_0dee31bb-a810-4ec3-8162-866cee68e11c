package domain

import (
	"time"
)

type AssignmentStatus string

const (
	AssignmentStatusActive  AssignmentStatus = "active"
	AssignmentStatusClosed  AssignmentStatus = "closed"
	AssignmentStatusPending AssignmentStatus = "pending"
)

type Assignment struct {
	ID uint `gorm:"primaryKey;autoIncrement" json:"id"` // ---

	// System Information
	Status           AssignmentStatus `gorm:"size:50;not null;default:'active'" json:"status"` // ---
	CreatedAt        time.Time        // ---
	UpdatedAt        time.Time
	CreatedBy        User       `gorm:"constraint:OnUpdate:CASCADE,OnDelete:SET NULL;" json:"createdBy"`      // ---
	CreatedByID      uint       `gorm:"index" json:"createdById"`                                             // ---
	AssignmentLead   Consultant `gorm:"constraint:OnUpdate:CASCADE,OnDelete:SET NULL;" json:"assignmentLead"` // ---
	AssignmentLeadID uint       `gorm:"index" json:"assignmentLeadId"`                                        // ---

	// Foreign key relationships
	Adjuster     Adjuster              `gorm:"constraint:OnUpdate:CASCADE,OnDelete:SET NULL;" json:"adjuster"`             // ---
	AdjusterID   uint                  `gorm:"index" json:"adjusterId"`                                                    // ---
	Carriers     []*Carrier            `gorm:"many2many:assignment_carriers;constraint:OnUpdate:CASCADE,OnDelete:CASCADE"` // ---
	CategoryData []*AssignmentCategory `gorm:"foreignKey:AssignmentID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE;" json:"categoryData"`

	// Assignment Information
	ClaimNumber        string    `gorm:"size:100;not null" json:"claimNumber"`                  // ---
	InsuredName        string    `gorm:"size:255;not null" json:"insuredName"`                  // ---
	AssignmentName     string    `gorm:"size:500;not null" json:"assignmentName"`               // ---
	DateOfLoss         time.Time `json:"dateOfLoss"`                                            // ---
	PrimaryContact     Contact   `gorm:"constraint:OnDelete:SET NULL;" json:"primaryContact"`   // ---
	PrimaryContactID   uint      `gorm:"index" json:"primaryContactId"`                         // ---
	SecondaryContact   *Contact  `gorm:"constraint:OnDelete:SET NULL;" json:"secondaryContact"` // ---
	SecondaryContactID *uint     `gorm:"index" json:"secondaryContactId"`                       // ---

	// Location Information
	LossLocation   Location `gorm:"constraint:OnDelete:CASCADE;" json:"lossLocation"` // ---
	LossLocationID uint     `gorm:"index" json:"lossLocationId"`                      // ---

	// Detailed Information
	ClaimDetail           *string    `gorm:"type:text" json:"claimDetail"`                                                          // ---
	AssignmentDescription *string    `gorm:"type:text" json:"assignmentDescription"`                                                // ---
	Comments              []*Comment `gorm:"foreignKey:AssignmentID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE;" json:"comments"` // ---
	Rom                   *Rom       `gorm:"constraint:OnUpdate:CASCADE,OnDelete:SET NULL;" json:"rom"`
	RomID                 *uint      `gorm:"index" json:"romId"`
}
