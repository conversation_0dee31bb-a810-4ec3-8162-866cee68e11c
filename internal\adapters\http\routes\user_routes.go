package routes

import (
	"github.com/SneatX/phillips_go/internal/adapters/http/handlers"
	"github.com/SneatX/phillips_go/internal/adapters/http/middleware"
	jwt "github.com/SneatX/phillips_go/internal/platform/token"
	"github.com/gin-gonic/gin"
)

func RegisterUserRoutes(
	rg *gin.RouterGroup,
	jwtManager *jwt.JWTManager,
	userHandler *handlers.UserHandler,
) {
	publicUsers := rg.Group("/users")
	{
		publicUsers.POST("", userHandler.CreateUser)
	}

	protectedUsers := publicUsers.Group("", middleware.JWTAuthMiddleware(jwtManager))
	{
		protectedUsers.GET("", userHandler.GetAllUsers)
		protectedUsers.GET("/me", userHandler.GetMyProfile)
		protectedUsers.GET("/:id", userHandler.GetUserByID)
	}
}
