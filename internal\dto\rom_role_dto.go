package dto

import (
	"time"

	"github.com/SneatX/phillips_go/internal/core/domain"
)

type RomRoleResponse struct {
	RomID     uint         `json:"romId"`
	RoleID    uint         `json:"roleId"`
	Quantity  float64      `json:"quantity"`
	Role      RoleResponse `json:"role"`
	CreatedAt time.Time    `json:"createdAt"`
	UpdatedAt time.Time    `json:"updatedAt"`
}

func FromDomainRomRole(romRole domain.RomRole) RomRoleResponse {
	return RomRoleResponse{
		RomID:     romRole.RomID,
		RoleID:    romRole.RoleID,
		Quantity:  romRole.Quantity,
		Role:      FromDomainRole(romRole.Role),
		CreatedAt: romRole.CreatedAt,
		UpdatedAt: romRole.UpdatedAt,
	}
}

type RomRoleListResponse struct {
	Results []RomRoleResponse `json:"results"`
	Total   int               `json:"total"`
	Message string            `json:"message"`
}

func NewRomRoleListResponse(romRoles []*domain.RomRole, message string) RomRoleListResponse {
	romRolesResponse := make([]RomRoleResponse, len(romRoles))
	for i, romRole := range romRoles {
		romRolesResponse[i] = FromDomainRomRole(*romRole)
	}
	return RomRoleListResponse{
		Results: romRolesResponse,
		Total:   len(romRoles),
		Message: message,
	}
}

type AddRoleToRomRequest struct {
	RomID    uint    `json:"romId" binding:"required"`
	RoleID   uint    `json:"roleId" binding:"required"`
	Quantity float64 `json:"quantity" binding:"required,min=0"`
}

func ToDomainRomRole(r *AddRoleToRomRequest) *domain.RomRole {
	return &domain.RomRole{
		RomID:    r.RomID,
		RoleID:   r.RoleID,
		Quantity: r.Quantity,
	}
}

type UpdateRoleQuantityRequest struct {
	Quantity float64 `json:"quantity" binding:"required,min=0"`
}

func ToUpdateDomainRomRole(r *UpdateRoleQuantityRequest, romID, roleID uint) *domain.RomRole {
	return &domain.RomRole{
		RomID:    romID,
		RoleID:   roleID,
		Quantity: r.Quantity,
	}
}

type RomRolesResponse struct {
	RomID   uint              `json:"romId"`
	Roles   []RomRoleResponse `json:"roles"`
	Total   int               `json:"total"`
	Message string            `json:"message"`
}

func NewRomRolesResponse(romID uint, romRoles []*domain.RomRole, message string) RomRolesResponse {
	rolesResponse := make([]RomRoleResponse, len(romRoles))
	for i, romRole := range romRoles {
		rolesResponse[i] = FromDomainRomRole(*romRole)
	}
	return RomRolesResponse{
		RomID:   romID,
		Roles:   rolesResponse,
		Total:   len(romRoles),
		Message: message,
	}
}

type RoleRomsResponse struct {
	RoleID  uint              `json:"roleId"`
	Roms    []RomRoleResponse `json:"roms"`
	Total   int               `json:"total"`
	Message string            `json:"message"`
}

func NewRoleRomsResponse(roleID uint, romRoles []*domain.RomRole, message string) RoleRomsResponse {
	romsResponse := make([]RomRoleResponse, len(romRoles))
	for i, romRole := range romRoles {
		romsResponse[i] = FromDomainRomRole(*romRole)
	}
	return RoleRomsResponse{
		RoleID:  roleID,
		Roms:    romsResponse,
		Total:   len(romRoles),
		Message: message,
	}
}
