package dto

import (
	"time"

	"github.com/SneatX/phillips_go/internal/core/domain"
)

type RomRoleResponse struct {
	AssignmentID uint         `json:"assignmentId"`
	RoleID       uint         `json:"roleId"`
	Quantity     float64      `json:"quantity"`
	Role         RoleResponse `json:"role"`
	CreatedAt    time.Time    `json:"createdAt"`
	UpdatedAt    time.Time    `json:"updatedAt"`
}

func FromDomainRomRole(romRole domain.RomRole) RomRoleResponse {
	return RomRoleResponse{
		AssignmentID: romRole.AssignmentID,
		RoleID:       romRole.RoleID,
		Quantity:     romRole.Quantity,
		Role:         FromDomainRole(romRole.Role),
		CreatedAt:    romRole.CreatedAt,
		UpdatedAt:    romRole.UpdatedAt,
	}
}

type RomRoleListResponse struct {
	Results []RomRoleResponse `json:"results"`
	Total   int               `json:"total"`
	Message string            `json:"message"`
}

func NewRomRoleListResponse(romRoles []*domain.RomRole, message string) RomRoleListResponse {
	romRolesResponse := make([]RomRoleResponse, len(romRoles))
	for i, romRole := range romRoles {
		romRolesResponse[i] = FromDomainRomRole(*romRole)
	}
	return RomRoleListResponse{
		Results: romRolesResponse,
		Total:   len(romRoles),
		Message: message,
	}
}

type AddRoleToAssignmentRequest struct {
	AssignmentID uint    `json:"assignmentId" binding:"required"`
	RoleID       uint    `json:"roleId" binding:"required"`
	Quantity     float64 `json:"quantity" binding:"required,min=0"`
}

func ToDomainRomRole(r *AddRoleToAssignmentRequest) *domain.RomRole {
	return &domain.RomRole{
		AssignmentID: r.AssignmentID,
		RoleID:       r.RoleID,
		Quantity:     r.Quantity,
	}
}

type UpdateRoleQuantityRequest struct {
	Quantity float64 `json:"quantity" binding:"required,min=0"`
}

func ToUpdateDomainRomRole(r *UpdateRoleQuantityRequest, assignmentID, roleID uint) *domain.RomRole {
	return &domain.RomRole{
		AssignmentID: assignmentID,
		RoleID:       roleID,
		Quantity:     r.Quantity,
	}
}

type AssignmentRolesResponse struct {
	AssignmentID uint                `json:"assignmentId"`
	Roles        []RomRoleResponse   `json:"roles"`
	Total        int                 `json:"total"`
	Message      string              `json:"message"`
}

func NewAssignmentRolesResponse(assignmentID uint, romRoles []*domain.RomRole, message string) AssignmentRolesResponse {
	rolesResponse := make([]RomRoleResponse, len(romRoles))
	for i, romRole := range romRoles {
		rolesResponse[i] = FromDomainRomRole(*romRole)
	}
	return AssignmentRolesResponse{
		AssignmentID: assignmentID,
		Roles:        rolesResponse,
		Total:        len(romRoles),
		Message:      message,
	}
}

type RoleAssignmentsResponse struct {
	RoleID      uint                `json:"roleId"`
	Assignments []RomRoleResponse   `json:"assignments"`
	Total       int                 `json:"total"`
	Message     string              `json:"message"`
}

func NewRoleAssignmentsResponse(roleID uint, romRoles []*domain.RomRole, message string) RoleAssignmentsResponse {
	assignmentsResponse := make([]RomRoleResponse, len(romRoles))
	for i, romRole := range romRoles {
		assignmentsResponse[i] = FromDomainRomRole(*romRole)
	}
	return RoleAssignmentsResponse{
		RoleID:      roleID,
		Assignments: assignmentsResponse,
		Total:       len(romRoles),
		Message:     message,
	}
}
