package handlers

import (
	"path/filepath"
	"strconv"

	"github.com/SneatX/phillips_go/internal/adapters/http/middleware"
	"github.com/SneatX/phillips_go/internal/adapters/http/response"
	"github.com/SneatX/phillips_go/internal/core/port/in"
	"github.com/SneatX/phillips_go/internal/dto"
	appErrors "github.com/SneatX/phillips_go/internal/shared/errors"
	"github.com/gin-gonic/gin"
)

type DocumentHandler struct {
	documentService in.DocumentService
	baseDir         string
}

func NewDocumentHandler(service in.DocumentService, baseDir string) *DocumentHandler {
	return &DocumentHandler{
		documentService: service,
		baseDir:         baseDir,
	}
}

func (h *DocumentHandler) GetDocumentsByAssignmentID(c *gin.Context) {
	assignmentIDStr := c.Param("assignmentId")
	assignmentID, err := strconv.ParseUint(assignmentIDStr, 10, 32)
	if err != nil {
		response.ErrorResponse(c, appErrors.NewValidationError("Invalid assignment ID", err))
		return
	}

	documents, err := h.documentService.GetByAssignmentID(uint(assignmentID))
	if err != nil {
		_ = c.Error(err)
		return
	}

	response.SuccessMany(c, documents)
}

func (h *DocumentHandler) CreateDocumentIntent(c *gin.Context) {
	claims, exists := middleware.GetUserClaimsFromContext(c)
	if !exists {
		response.ErrorResponse(c, appErrors.NewUnauthorizedError("User claims not found in context. Authentication required."))
		return
	}

	var req dto.CreateDocumentIntentRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ErrorResponse(c, appErrors.NewValidationError("Invalid request body", err))
		return
	}

	intentToCreate := dto.ToDomainDocumentIntent(&req, claims.UserID)
	createdIntent, token, err := h.documentService.CreateIntent(intentToCreate)
	if err != nil {
		_ = c.Error(err)
		return
	}

	res := dto.CreateDocumentIntentResponse{
		Document:    dto.FromDomainDocument(*createdIntent),
		UploadToken: token,
	}

	response.SuccessOne(c, res)
}

func (h *DocumentHandler) CreateDocumentUpload(c *gin.Context) {
	token := c.Query("token")
	if token == "" {
		response.ErrorResponse(c, appErrors.NewValidationError("missing upload token", nil))
		return
	}

	file, hdr, err := c.Request.FormFile("file")
	if err != nil {
		response.ErrorResponse(c, appErrors.NewValidationError("missing file", err))
		return
	}
	defer file.Close()

	doc, err := h.documentService.UploadDocument(token, hdr.Filename, file)
	if err != nil {
		_ = c.Error(err)
		return
	}

	response.SuccessOne(c, dto.FromDomainDocument(*doc))
}

func (h *DocumentHandler) DownloadAssignmentBundle(c *gin.Context) {
	assignmentIDStr := c.Param("assignmentId")
	assignmentID, err := strconv.ParseUint(assignmentIDStr, 10, 32)
	if err != nil {
		response.ErrorResponse(c, appErrors.NewValidationError("Invalid assignment ID", err))
		return
	}

	docs, err := h.documentService.GetUploadedByAssignmentID(uint(assignmentID))
	if err != nil {
		_ = c.Error(err)
		return
	}

	zipRelPath, zipFileName, err := h.documentService.PrepareAssignmentZip(uint(assignmentID), docs)
	if err != nil {
		_ = c.Error(err)
		return
	}

	// c.Header("Content-Disposition", "attachment; filename*=UTF-8''"+url.PathEscape(zipFileName))
	// c.Header("Content-Type", "application/zip")
	// c.Header("X-Accel-Redirect", "/protected/blobs/"+zipRelPath)
	// c.Status(200)

	abs := filepath.Join(h.baseDir, zipRelPath)
	c.Header("Content-Type", "application/zip")
	c.FileAttachment(abs, zipFileName)
}
