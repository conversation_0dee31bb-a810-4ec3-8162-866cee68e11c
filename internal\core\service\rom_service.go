package service

import (
	"github.com/SneatX/phillips_go/internal/core/domain"
	"github.com/SneatX/phillips_go/internal/core/port/in"
	"github.com/SneatX/phillips_go/internal/core/port/out"
)

type romService struct {
	romRepo        out.RomRepository
	assignmentRepo out.AssignmentRepository
}

func NewRomService(romRepo out.RomRepository, assignmentRepo out.AssignmentRepository) in.RomService {
	return &romService{
		romRepo:        romRepo,
		assignmentRepo: assignmentRepo,
	}
}

func (s *romService) Create(rom *domain.Rom) (*domain.Rom, error) {
	createdRom, err := s.romRepo.Create(rom)
	if err != nil {
		return nil, err
	}

	assignment, err := s.assignmentRepo.GetByID(rom.AssignmentID)
	if err != nil {
		return nil, err
	}
	assignment.RomID = &createdRom.ID
	_, err = s.assignmentRepo.Update(assignment)
	if err != nil {
		return nil, err
	}

	return createdRom, nil
}

func (s *romService) Update(rom *domain.Rom) (*domain.Rom, error) {
	return s.romRepo.Update(rom)
}
