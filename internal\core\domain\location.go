package domain

import "time"

type Location struct {
	ID         uint   `gorm:"primaryKey" json:"id"`
	Street     string `gorm:"size:255" json:"street"`
	City       string `gorm:"size:100" json:"city"`
	State      string `gorm:"size:100" json:"state"`
	PostalCode string `gorm:"size:20" json:"postalCode"`
	Country    string `gorm:"size:100" json:"country"`
	CreatedAt  time.Time
	UpdatedAt  time.Time
}
