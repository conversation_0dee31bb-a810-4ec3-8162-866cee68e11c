package service

import (
	"github.com/SneatX/phillips_go/internal/core/domain"
	"github.com/SneatX/phillips_go/internal/core/port/in"
	"github.com/SneatX/phillips_go/internal/core/port/out"
)

type roleService struct {
	roleRepo out.RoleRepository
}

func NewRoleService(repo out.RoleRepository) in.RoleService {
	return &roleService{roleRepo: repo}
}

func (s *roleService) GetAll() ([]*domain.Role, error) {
	return s.roleRepo.GetAll()
}

func (s *roleService) GetByID(id uint) (*domain.Role, error) {
	return s.roleRepo.GetByID(id)
}

func (s *roleService) Create(role *domain.Role) (*domain.Role, error) {
	return s.roleRepo.Create(role)
}

func (s *roleService) Update(role *domain.Role) (*domain.Role, error) {
	return s.roleRepo.Update(role)
}

func (s *roleService) Delete(id uint) error {
	return s.roleRepo.Delete(id)
}
