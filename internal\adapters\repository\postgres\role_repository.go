package postgres

import (
	"errors"
	"fmt"

	"github.com/SneatX/phillips_go/internal/core/domain"
	appErrors "github.com/SneatX/phillips_go/internal/shared/errors"
	"github.com/jackc/pgx/v5/pgconn"
	"gorm.io/gorm"
)

type roleRepository struct {
	DB *gorm.DB
}

func NewRoleRepository(DB *gorm.DB) *roleRepository {
	return &roleRepository{DB: DB}
}

func (r *roleRepository) GetAll() ([]*domain.Role, error) {
	var roles []*domain.Role
	if err := r.DB.Find(&roles).Error; err != nil {
		return nil, appErrors.NewInternalError("failed to retrieve all roles", err)
	}
	return roles, nil
}

func (r *roleRepository) GetByID(id uint) (*domain.Role, error) {
	var role domain.Role
	if err := r.DB.First(&role, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, appErrors.NewNotFoundError(fmt.Sprintf("role with ID %d not found", id))
		}
		return nil, appErrors.NewInternalError("failed to retrieve role", err)
	}
	return &role, nil
}

func (r *roleRepository) Create(role *domain.Role) (*domain.Role, error) {
	if result := r.DB.Create(&role); result.Error != nil {
		var pgErr *pgconn.PgError
		if errors.As(result.Error, &pgErr) {
			switch pgErr.Code {
			case "23505": // unique_violation
				if pgErr.ConstraintName == "roles_name_key" {
					return nil, appErrors.NewConflictError("A role with this name already exists")
				}
				if pgErr.ConstraintName == "roles_value_key" {
					return nil, appErrors.NewConflictError("A role with this value already exists")
				}
				return nil, appErrors.NewConflictError("Role already exists")
			}
		}
		return nil, appErrors.NewInternalError("failed to create role", result.Error)
	}
	return role, nil
}

func (r *roleRepository) Update(role *domain.Role) (*domain.Role, error) {
	if result := r.DB.Model(&role).Updates(&role); result.Error != nil {
		var pgErr *pgconn.PgError
		if errors.As(result.Error, &pgErr) {
			switch pgErr.Code {
			case "23505": // unique_violation
				if pgErr.ConstraintName == "roles_name_key" {
					return nil, appErrors.NewConflictError("A role with this name already exists")
				}
				if pgErr.ConstraintName == "roles_value_key" {
					return nil, appErrors.NewConflictError("A role with this value already exists")
				}
				return nil, appErrors.NewConflictError("Role already exists")
			}
		}
		return nil, appErrors.NewInternalError("failed to update role", result.Error)
	}
	return role, nil
}

func (r *roleRepository) Delete(id uint) error {
	result := r.DB.Delete(&domain.Role{}, id)
	if result.Error != nil {
		return appErrors.NewInternalError("failed to delete role", result.Error)
	}
	if result.RowsAffected == 0 {
		return appErrors.NewNotFoundError(fmt.Sprintf("role with ID %d not found", id))
	}
	return nil
}
