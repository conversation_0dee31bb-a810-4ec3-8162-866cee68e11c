package postgres

import (
	"errors"
	"fmt"

	"github.com/SneatX/phillips_go/internal/core/domain"
	appErrors "github.com/SneatX/phillips_go/internal/shared/errors"
	"github.com/jackc/pgx/v5/pgconn"
	"gorm.io/gorm"
)

type documentRepository struct {
	DB *gorm.DB
}

func NewDocumentRepository(DB *gorm.DB) *documentRepository {
	return &documentRepository{DB: DB}
}

func (r *documentRepository) GetByID(id uint) (*domain.Document, error) {
	var document domain.Document
	result := r.DB.
		Preload("Assignment").
		Preload("CreatedBy").
		First(&document, id)
	return &document, result.Error
}

func (r *documentRepository) GetByAssignmentID(assignmentID uint) ([]*domain.Document, error) {
	var documents []*domain.Document

	if err := r.DB.
		Preload("Assignment").
		Preload("CreatedBy").
		Where("assignment_id = ?", assignmentID).
		Find(&documents).Error; err != nil {
		return nil, err
	}

	return documents, nil
}

func (r *documentRepository) GetUploadedByAssignmentID(assignmentID uint) ([]*domain.Document, error) {
	var documents []*domain.Document

	if err := r.DB.
		Preload("Assignment").
		Preload("CreatedBy").
		Where("assignment_id = ? AND status = 'uploaded'", assignmentID).
		Find(&documents).Error; err != nil {
		return nil, err
	}

	return documents, nil
}

func (r *documentRepository) CreateIntent(document *domain.Document) (*domain.Document, error) {
	if result := r.DB.Create(document); result.Error != nil {
		var pgErr *pgconn.PgError
		if errors.As(result.Error, &pgErr) {
			fmt.Printf("PostgreSQL error code: %s, message: %s\n", pgErr.Code, pgErr.Message)
			if pgErr.Code == "23503" {
				return nil, appErrors.NewNotFoundError("foreign key constraint violation, please check that all related entities exist before creating a document")
			}
		}
	}

	if err := r.DB.
		Preload("Assignment").
		Preload("CreatedBy").
		First(&document, document.ID).Error; err != nil {
		return nil, appErrors.NewInternalError("failed to load created document", err)
	}

	return document, nil
}

func (r *documentRepository) UpdateDocument(document *domain.Document) (*domain.Document, error) {
	var existing domain.Document
	if err := r.DB.First(&existing, document.ID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, appErrors.NewNotFoundError("document not found")
		}
		return nil, appErrors.NewInternalError("failed to find document for update", err)
	}

	if err := r.DB.
		Preload("Assignment").
		Preload("CreatedBy").
		Model(&existing).
		Updates(document).Error; err != nil {
		return nil, err
	}

	return &existing, nil
}
